<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>EQUIP_PARAM_GOODS_ST</ParamType>
  <DataVersion>3</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>100000</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>100001</SortID>
    </Field>
    <Field Def="s32 refId_default = -1">
      <DisplayName>呼び出しID0(デフォルト)</DisplayName>
      <Description>アイテムから呼び出されるID0(デフォルト)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="s32 sfxVariationId = -1">
      <DisplayName>SFXバリエーションID</DisplayName>
      <Description>ＳＦＸのバリエーションを指定（TimeActEditorのＩＤと組み合わせて、ＳＦＸを特定するのに使用する）</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>99999</SortID>
    </Field>
    <Field Def="f32 weight = 1">
      <DisplayName>重量[kg]</DisplayName>
      <Description>重量[kg]</Description>
      <DisplayFormat>%d</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>900</SortID>
    </Field>
    <Field Def="s32 basicPrice">
      <DisplayName>基本価格</DisplayName>
      <Description>基本価格</Description>
      <Minimum>0</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="s32 sellValue">
      <DisplayName>売却価格</DisplayName>
      <Description>販売価格</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1210</SortID>
    </Field>
    <Field Def="s32 behaviorId">
      <DisplayName>行動ID</DisplayName>
      <Description>道具を使ったときに発生する効果を設定します</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="s32 replaceItemId = -1">
      <DisplayName>差し替えアイテムID</DisplayName>
      <Description>差し替えるときのアイテムID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>3500</SortID>
    </Field>
    <Field Def="s32 sortId">
      <DisplayName>ソートID</DisplayName>
      <Description>ソートID(-1:集めない)</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>3600</SortID>
    </Field>
    <Field Def="s32 appearanceReplaceItemId = -1">
      <DisplayName>表示差し替え先アイテムID</DisplayName>
      <Description>条件付きで表示を別の道具IDに差し替える。ゲームデータ側の道具IDは変わらない</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>4400</SortID>
    </Field>
    <Field Def="s32 yesNoDialogMessageId = -1">
      <DisplayName>YES/NOメッセージID</DisplayName>
      <Description>YesNoダイアログ表示時に使用するメッセージID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>4300</SortID>
    </Field>
    <Field Def="u16 useEnableSpEffectType">
      <DisplayName>使用可能条件_状態変化タイプ</DisplayName>
      <Enum>SP_EFFECT_TYPE</Enum>
      <Description>設定した状態変化タイプの特殊効果が掛かったときだけ、使用許可する</Description>
      <Maximum>65000</Maximum>
      <SortID>1621</SortID>
    </Field>
    <Field Def="s8 potGroupId = -1">
      <DisplayName>壺グループID</DisplayName>
      <Description>壺グループIDに0以上が設定されている「消費アイテム」は同じ壺グループIDの「壺」の個数だけ所持可能</Description>
      <Minimum>-1</Minimum>
      <Maximum>15</Maximum>
      <SortID>3960</SortID>
    </Field>
    <Field Def="dummy8 pad[1]">
      <DisplayName>PAD</DisplayName>
      <Description>旧(巻物と紐づいた魔法ID)</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>100002</SortID>
    </Field>
    <Field Def="u16 iconId">
      <DisplayName>アイコンID</DisplayName>
      <Description>メニュー用アイコンID</Description>
      <SortID>100</SortID>
    </Field>
    <Field Def="u16 modelId">
      <DisplayName>モデルID</DisplayName>
      <Description>モデルID</Description>
      <Maximum>9999</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="s16 shopLv">
      <DisplayName>ショップレベル</DisplayName>
      <Description>お店で販売できるレベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>2800</SortID>
    </Field>
    <Field Def="s16 compTrophySedId = -1">
      <DisplayName>コンプトロフィーSEQ番号</DisplayName>
      <Description>コンプリート系トロフィのSEQ番号</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>3700</SortID>
    </Field>
    <Field Def="s16 trophySeqId = -1">
      <DisplayName>トロフィーSEQ番号</DisplayName>
      <Description>トロフィーのSEQ番号</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>3800</SortID>
    </Field>
    <Field Def="s16 maxNum">
      <DisplayName>最大所持数</DisplayName>
      <Description>最大所持数</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>610</SortID>
    </Field>
    <Field Def="u8 consumeHeroPoint">
      <DisplayName>消費人間性</DisplayName>
      <Description>消費人間性</Description>
      <SortID>612</SortID>
    </Field>
    <Field Def="u8 overDexterity">
      <DisplayName>技量オーバー開始値</DisplayName>
      <Description>技量オーバー開始値</Description>
      <Maximum>99</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="u8 goodsType">
      <DisplayName>道具のタイプ</DisplayName>
      <Enum>GOODS_TYPE</Enum>
      <Description>道具の種類</Description>
      <Maximum>99</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u8 refCategory">
      <DisplayName>IDカテゴリ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>↓のIDのカテゴリ[攻撃、飛び道具、特殊]</Description>
      <SortID>300</SortID>
    </Field>
    <Field Def="u8 spEffectCategory">
      <DisplayName>特殊効果カテゴリ</DisplayName>
      <Enum>BEHAVIOR_CATEGORY</Enum>
      <Description>スキルや、魔法、アイテムなどで、パラメータが変動する効果（エンチャントウェポンなど）があるので、│定した効果が、「武器攻撃のみをパワーアップする」といった効果に対応できるように行動ごとに設定するバリスタなど、設定の必要のないものは「なし」を設定する
</Description>
      <SortID>600</SortID>
    </Field>
    
    <Field Def="dummy8 pad3[1]" RemovedVersion="11210015">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>100003</SortID>
    </Field>
    
    <Field Def="u8 unknown_0x40" FirstVersion="11210015" />
    
    <Field Def="u8 goodsUseAnim">
      <DisplayName>道具使用時アニメ</DisplayName>
      <Enum>GOODS_USE_ANIM</Enum>
      <Description>道具を使ったときに再生するアニメを設定します</Description>
      <Maximum>99</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="u8 opmeMenuType">
      <DisplayName>メニュー開くか</DisplayName>
      <Enum>GOODS_OPEN_MENU</Enum>
      <Description>アイテム使用時に開くメニュータイプ</Description>
      <SortID>4100</SortID>
    </Field>
    <Field Def="u8 useLimitCategory">
      <DisplayName>使用禁止条件_特殊効果カテゴリ</DisplayName>
      <Enum>SP_EFFECT_USELIMIT_CATEGORY</Enum>
      <Description>かかっている特殊効果によって使用可能かを制御する為に指定</Description>
      <SortID>1610</SortID>
    </Field>
    <Field Def="u8 replaceCategory">
      <DisplayName>差し替えカテゴリ</DisplayName>
      <Enum>REPLACE_CATEGORY</Enum>
      <Description>呼び出しIDに加算しる条件カテゴリ</Description>
      <SortID>15001</SortID>
    </Field>
    <Field Def="dummy8 reserve4[2]">
      <DisplayName>リザーブ</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>100004</SortID>
    </Field>
    <Field Def="u8 enable_live:1">
      <DisplayName>生存使用可</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>生存プレイヤー使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>1710</SortID>
    </Field>
    <Field Def="u8 enable_gray:1">
      <DisplayName>グレイ使用可</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>グレイゴースト使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>1800</SortID>
    </Field>
    <Field Def="u8 enable_white:1">
      <DisplayName>白使用可</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>ホワイトゴースト使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>1900</SortID>
    </Field>
    <Field Def="u8 enable_black:1">
      <DisplayName>黒使用可</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>ブラックゴーストしよう可能か</Description>
      <Maximum>1</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="u8 enable_multi:1">
      <DisplayName>マルチプレイ可</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>マルチプレイ中に使用可能か？</Description>
      <Maximum>1</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="u8 disable_offline:1">
      <DisplayName>オフラインで使用不可</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>オフライン中に使用不可か？</Description>
      <Maximum>1</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="u8 isEquip:1">
      <DisplayName>装備可能</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>装備できるかどうか</Description>
      <Maximum>1</Maximum>
      <SortID>2400</SortID>
    </Field>
    <Field Def="u8 isConsume:1">
      <DisplayName>消耗品か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>使用時に消耗するか(所持数が減るか)</Description>
      <Maximum>1</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="u8 isAutoEquip:1">
      <DisplayName>自動装備するか？</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>拾った時に自動で装備するか？</Description>
      <Maximum>1</Maximum>
      <SortID>2600</SortID>
    </Field>
    <Field Def="u8 isEstablishment:1">
      <DisplayName>設置型アイテムか？</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>設置型アイテムか？</Description>
      <Maximum>1</Maximum>
      <SortID>2700</SortID>
    </Field>
    <Field Def="u8 isOnlyOne:1">
      <DisplayName>1個しか持てないか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>1個しか持てないアイテムか</Description>
      <Maximum>1</Maximum>
      <SortID>2900</SortID>
    </Field>
    <Field Def="u8 isDiscard:1">
      <DisplayName>捨てれるか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>アイテムを捨てれるか？TRUE=捨てれる</Description>
      <Maximum>1</Maximum>
      <SortID>3100</SortID>
    </Field>
    <Field Def="u8 isDeposit:1">
      <DisplayName>預けれるか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>倉庫に預けれるか</Description>
      <Maximum>1</Maximum>
      <SortID>3150</SortID>
    </Field>
    <Field Def="u8 isDisableHand:1">
      <DisplayName>右素手に使えないか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>右手武器が素手の場合に使用不可か</Description>
      <Maximum>1</Maximum>
      <SortID>3200</SortID>
    </Field>
    <Field Def="u8 isRemoveItem_forGameClear:1">
      <DisplayName>周回時削除するか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>周回時削除するか</Description>
      <Maximum>1</Maximum>
      <SortID>21800</SortID>
    </Field>
    <Field Def="u8 isSuppleItem:1">
      <DisplayName>補充アイテムか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>補充可能アイテムを判別するのに使用します</Description>
      <Maximum>1</Maximum>
      <SortID>3350</SortID>
    </Field>
    <Field Def="u8 isFullSuppleItem:1">
      <DisplayName>補充済みアイテムか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>補充済みアイテムを判別するのに使用します</Description>
      <Maximum>1</Maximum>
      <SortID>3351</SortID>
    </Field>
    <Field Def="u8 isEnhance:1">
      <DisplayName>エンチャントするか？</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>武器にエンチャントするか？</Description>
      <Maximum>1</Maximum>
      <SortID>4000</SortID>
    </Field>
    <Field Def="u8 isFixItem:1">
      <DisplayName>修理アイテムか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>修理するアイテムか？</Description>
      <Maximum>1</Maximum>
      <SortID>3250</SortID>
    </Field>
    <Field Def="u8 disableMultiDropShare:1">
      <DisplayName>マルチドロップ共有禁止か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>マルチドロップ共有禁止か</Description>
      <Maximum>1</Maximum>
      <SortID>3155</SortID>
    </Field>
    <Field Def="u8 disableUseAtColiseum:1">
      <DisplayName>闘技場で使用禁止か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>闘技場で使用禁止か</Description>
      <Maximum>1</Maximum>
      <SortID>3160</SortID>
    </Field>
    <Field Def="u8 disableUseAtOutOfColiseum:1">
      <DisplayName>闘技場以外で使用禁止か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>闘技場以外で使用禁止か</Description>
      <Maximum>1</Maximum>
      <SortID>3160</SortID>
    </Field>
    <Field Def="u8 isEnableFastUseItem:1">
      <DisplayName>早いキャンセル可能か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>早いキャンセル可能か</Description>
      <Maximum>1</Maximum>
      <SortID>15100</SortID>
    </Field>
    <Field Def="u8 isApplySpecialEffect:1">
      <DisplayName>特殊効果を反映するか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>（能力値補正など）特殊効果を反映するか</Description>
      <Maximum>1</Maximum>
      <SortID>4020</SortID>
    </Field>
    <Field Def="u8 syncNumVaryId">
      <DisplayName>個数増減を同期させるID</DisplayName>
      <Description>アイテムの個数が変更された際に、同じIDを設定したアイテムも一緒に変更を行います。 0：同期しない</Description>
      <SortID>3950</SortID>
    </Field>
    <Field Def="s32 refId_1 = -1">
      <DisplayName>呼び出しID1</DisplayName>
      <Description>アイテムから呼び出されるID1</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>410</SortID>
    </Field>
    <Field Def="s32 refVirtualWepId = -1">
      <DisplayName>参照仮想武器ID</DisplayName>
      <Description>道具使用時に参照する武器ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>250</SortID>
    </Field>
    <Field Def="s32 vagrantItemLotId">
      <DisplayName>ベイグラント時アイテム抽選ID_マップ用</DisplayName>
      <Description>-1：ベイグラントなし 0：抽選なし 1～：抽選あり</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="s32 vagrantBonusEneDropItemLotId">
      <DisplayName>ベイグラントボーナス敵ドロップアイテム抽選ID_マップ用</DisplayName>
      <Description>-1：ドロップなし 0：抽選なし 1～：抽選あり</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="s32 vagrantItemEneDropItemLotId">
      <DisplayName>ベイグラントアイテム敵ドロップアイテム抽選ID_マップ用</DisplayName>
      <Description>-1：ドロップなし 0：抽選なし 1～：抽選あり</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="s32 castSfxId = -1">
      <DisplayName>手持ちSFXID</DisplayName>
      <Description>アイテムを使用しようとし、効果が発動するまでのSFXID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="s32 fireSfxId = -1">
      <DisplayName>発動SFXID</DisplayName>
      <Description>アイテムが発動したときのSFXID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>501</SortID>
    </Field>
    <Field Def="s32 effectSfxId = -1">
      <DisplayName>効果SFXID</DisplayName>
      <Description>アイテムが発動後、効果中のSFXID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>502</SortID>
    </Field>
    <Field Def="u8 enable_ActiveBigRune:1">
      <DisplayName>大ルーン発動中使用可</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>大ルーン発動状態で使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="u8 isBonfireWarpItem:1">
      <DisplayName>篝火ワープアイテムか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>TRUEの時に状態変化タイプの「ワープ禁止」がかかっていればそのアイテムを使用不可にする機能を外す</Description>
      <Maximum>1</Maximum>
      <SortID>3310</SortID>
    </Field>
    <Field Def="u8 enable_Ladder:1">
      <DisplayName>はしご中使用可能か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>はしご中に使用可能なアイテムはここにチェックを入れます</Description>
      <Maximum>1</Maximum>
      <SortID>2210</SortID>
    </Field>
    <Field Def="u8 isUseMultiPlayPreparation:1">
      <DisplayName>マルチプレイ準備中可</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>セッション確率～初期同期の間でアイテムを使用できるかどうか</Description>
      <Maximum>1</Maximum>
      <SortID>2050</SortID>
    </Field>
    <Field Def="u8 canMultiUse:1">
      <DisplayName>まとめて使えるか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>まとめて使えるか</Description>
      <Maximum>1</Maximum>
      <SortID>2910</SortID>
    </Field>
    <Field Def="u8 isShieldEnchant:1">
      <DisplayName>盾エンチャントか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>盾エンチャントか</Description>
      <Maximum>1</Maximum>
      <SortID>4010</SortID>
    </Field>
    <Field Def="u8 isWarpProhibited:1">
      <DisplayName>ワープ禁止対象か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>これがTRUEの時に、状態変化タイプの「ワープ禁止」がかかっていればそのアイテムを使用不可にする </Description>
      <Maximum>1</Maximum>
      <SortID>3320</SortID>
    </Field>
    <Field Def="u8 isUseMultiPenaltyOnly:1">
      <DisplayName>切断ペナルティが発生しているときのみ使用可能</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>クライアント切断ペナルティが発生しているときのみ使用可能なアイテムかどうかを判断できるようにするためのフラグ</Description>
      <Maximum>1</Maximum>
      <SortID>2051</SortID>
    </Field>
    <Field Def="u8 suppleType">
      <DisplayName>補充タイプ</DisplayName>
      <Enum>GOODS_SUPPLE_TYPE</Enum>
      <Description>補充アイテム/補充済みアイテムを補充する際の補充タイプ。</Description>
      <Maximum>2</Maximum>
      <SortID>3352</SortID>
    </Field>
    <Field Def="u8 autoReplenishType">
      <DisplayName>自動補充タイプ</DisplayName>
      <Enum>AUTO_REPLENISH_TYPE</Enum>
      <Description>自動補充する/しないの可否およびデフォルト設定をコントロールします</Description>
      <SortID>3550</SortID>
    </Field>
    <Field Def="u8 isDrop:1">
      <DisplayName>その場に置けるか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>アイテムをその場に置けるか？TRUE=置ける</Description>
      <Maximum>1</Maximum>
      <SortID>3110</SortID>
    </Field>
    <Field Def="u8 showLogCondType:1 = 1">
      <DisplayName>取得ログ表示条件</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>アイテム取得時にアイテム取得ログに表示するか（未入力: ○）</Description>
      <Maximum>1</Maximum>
      <SortID>21000</SortID>
    </Field>
    <Field Def="u8 isSummonHorse:1">
      <DisplayName>馬呼びアイテムか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>馬を召喚するアイテムか？馬が死亡、PCが馬禁止エリアに居る場合は使用不可</Description>
      <Maximum>1</Maximum>
      <SortID>3330</SortID>
    </Field>
    <Field Def="u8 showDialogCondType:2 = 2">
      <DisplayName>取得ダイアログ表示条件</DisplayName>
      <Enum>GET_DIALOG_CONDITION_TYPE</Enum>
      <Description>アイテム取得時にアイテム取得ダイアログに表示するか（未入力: newのみ）</Description>
      <Maximum>2</Maximum>
      <SortID>20990</SortID>
    </Field>
    <Field Def="u8 isSleepCollectionItem:1">
      <DisplayName>ネムリ収集アイテムか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>ネムリ収集アイテムか</Description>
      <Maximum>1</Maximum>
      <SortID>3340</SortID>
    </Field>
    <Field Def="u8 enableRiding:1">
      <DisplayName>騎乗中使用可能か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>騎乗中使用可能か</Description>
      <Maximum>1</Maximum>
      <SortID>2220</SortID>
    </Field>
    <Field Def="u8 disableRiding:1">
      <DisplayName>非騎乗中使用禁止か</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>非騎乗中使用禁止か</Description>
      <Maximum>1</Maximum>
      <SortID>2230</SortID>
    </Field>
    <Field Def="s16 maxRepositoryNum">
      <DisplayName>倉庫所持数</DisplayName>
      <Description>倉庫所持数</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>611</SortID>
    </Field>
    <Field Def="u8 sortGroupId = 255">
      <DisplayName>ソートアイテム種別ID</DisplayName>
      <Description>ソートアイテム種別ID。ソート「アイテム種別順」にて、同じIDは同じグループとしてまとめて表示されます</Description>
      <SortID>3610</SortID>
    </Field>
    <Field Def="u8 isUseNoAttackRegion:1 = 1">
      <DisplayName>攻撃禁止区域で使用できるか</DisplayName>
      <Enum>EQUIP_BOOL</Enum>
      <Description>攻撃禁止区域で使用できるか</Description>
      <Maximum>1</Maximum>
      <SortID>2240</SortID>
    </Field>
    
    <Field Def="dummy8 pad1_old:7" RemovedVersion="11210015" />
    
    <Field Def="u8 unknown_0x73_1:1" FirstVersion="11210015" />
    <Field Def="u8 quickMatchReplenish:1" FirstVersion="11210015" />
    <Field Def="dummy8 pad1:5" FirstVersion="11210015" >
      <DisplayName>pad</DisplayName>
      <Description>（旧ログ用アイコン）</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>100005</SortID>
    </Field>
    
    <Field Def="s32 saleValue = -1">
      <DisplayName>販売価格</DisplayName>
      <Description>販売価格</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1220</SortID>
    </Field>
    <Field Def="u8 rarity">
      <DisplayName>レア度</DisplayName>
      <Description>アイテム取得ログで使うレア度</Description>
      <Maximum>99</Maximum>
      <SortID>21200</SortID>
    </Field>
    <Field Def="u8 useLimitSummonBuddy">
      <DisplayName>バディアイテムか</DisplayName>
      <Enum>GOODS_USELIMIT_SUMMONBUDDY</Enum>
      <Description>バディアイテム用のアイテム使用制限がかかるかどうか</Description>
      <SortID>2750</SortID>
    </Field>
    <Field Def="u16 useLimitSpEffectType">
      <DisplayName>使用禁止条件_状態変化タイプ</DisplayName>
      <Enum>SP_EFFECT_TYPE</Enum>
      <Description>かかっている特殊効果の状態変化タイプによって使用可能かを制御する為に指定</Description>
      <Maximum>65000</Maximum>
      <SortID>1620</SortID>
    </Field>
    <Field Def="s32 aiUseJudgeId = -1">
      <DisplayName>AI使用判断ID</DisplayName>
      <Description>AI使用判断ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>21300</SortID>
    </Field>
    <Field Def="s16 consumeMP">
      <DisplayName>消費MP</DisplayName>
      <Description>消費MP</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>650</SortID>
    </Field>
    <Field Def="s16 consumeHP = -1">
      <DisplayName>消費HP</DisplayName>
      <Description>消費HP</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>640</SortID>
    </Field>
    <Field Def="s32 reinforceGoodsId = -1">
      <DisplayName>強化先道具ID</DisplayName>
      <Description>強化先道具ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>21500</SortID>
    </Field>
    <Field Def="s32 reinforceMaterialId = -1">
      <DisplayName>強化時素材ID</DisplayName>
      <Description>強化時素材ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>21510</SortID>
    </Field>
    <Field Def="s32 reinforcePrice">
      <DisplayName>強化価格</DisplayName>
      <Description>強化価格</Description>
      <Minimum>0</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>21520</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType0">
      <DisplayName>誓約0使用レベル</DisplayName>
      <Description>誓約0使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21600</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType1">
      <DisplayName>誓約1使用レベル</DisplayName>
      <Description>誓約1使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21601</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType2">
      <DisplayName>誓約2使用レベル</DisplayName>
      <Description>誓約2使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21602</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType3">
      <DisplayName>誓約3使用レベル</DisplayName>
      <Description>誓約3使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21603</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType4">
      <DisplayName>誓約4使用レベル</DisplayName>
      <Description>誓約4使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21604</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType5">
      <DisplayName>誓約5使用レベル</DisplayName>
      <Description>誓約5使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21605</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType6">
      <DisplayName>誓約6使用レベル</DisplayName>
      <Description>誓約6使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21606</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType7">
      <DisplayName>誓約7使用レベル</DisplayName>
      <Description>誓約7使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21607</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType8">
      <DisplayName>誓約8使用レベル</DisplayName>
      <Description>誓約8使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21608</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType9">
      <DisplayName>誓約9使用レベル</DisplayName>
      <Description>誓約9使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21609</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType10">
      <DisplayName>誓約10使用レベル</DisplayName>
      <Description>誓約10使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21610</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType11">
      <DisplayName>誓約11使用レベル</DisplayName>
      <Description>誓約11使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21611</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType12">
      <DisplayName>誓約12使用レベル</DisplayName>
      <Description>誓約12使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21612</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType13">
      <DisplayName>誓約13使用レベル</DisplayName>
      <Description>誓約13使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21613</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType14">
      <DisplayName>誓約14使用レベル</DisplayName>
      <Description>誓約14使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21614</SortID>
    </Field>
    <Field Def="s8 useLevel_vowType15">
      <DisplayName>誓約15使用レベル</DisplayName>
      <Description>誓約15使用レベル</Description>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>21615</SortID>
    </Field>
    <Field Def="u16 useLevel">
      <DisplayName>使用適正レベル</DisplayName>
      <Description>使用適正レベル</Description>
      <Maximum>999</Maximum>
      <SortID>1605</SortID>
    </Field>
    <Field Def="dummy8 reserve5[2]">
      <DisplayName>予約領域</DisplayName>
      <Description>予約領域</Description>
      <SortID>100006</SortID>
    </Field>
    <Field Def="u32 itemGetTutorialFlagId">
      <DisplayName>アイテム入手チュートリアル判定フラグID</DisplayName>
      <Description>初めてアイテム入手した時のチュートリアル用のイベントフラグID。アイテム入手時にフラグON。</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>21700</SortID>
    </Field>
    
    <Field Def="s32 protectorOverrideId" />
    <Field Def="s32 bulletId" />
    <Field Def="s32 unknown_3" />
    
    <Field Def="dummy8 reserve3[12]">
      <DisplayName>予約領域</DisplayName>
      <Description>予約領域</Description>
      <SortID>100007</SortID>
    </Field>
  </Fields>
</PARAMDEF>