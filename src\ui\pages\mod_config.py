#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mod配置页面 - 管理和配置游戏Mod
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QGroupBox, QCheckBox, QTextEdit,
                               QScrollArea, QFrame, QSpacerItem, QSizePolicy,
                               QMessageBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
from pathlib import Path
import toml

from ...utils.logger import Logger
from ...utils.config import ConfigManager


class ModItem(QFrame):
    """Mod项目组件"""
    
    mod_toggled = Signal(str, bool)  # mod_id, enabled
    
    def __init__(self, mod_id: str, mod_name: str, mod_path: str, enabled: bool = False):
        super().__init__()
        self.mod_id = mod_id
        self.mod_name = mod_name
        self.mod_path = mod_path
        
        self._init_ui(enabled)
        
    def _init_ui(self, enabled: bool):
        """初始化UI"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 1px solid #3e3e3e;
                border-radius: 6px;
                background-color: #2d2d2d;
                margin: 2px;
            }
            QFrame:hover {
                border-color: #0078d4;
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 10, 15, 10)
        
        # 复选框
        self.checkbox = QCheckBox()
        self.checkbox.setChecked(enabled)
        self.checkbox.toggled.connect(lambda checked: self.mod_toggled.emit(self.mod_id, checked))
        layout.addWidget(self.checkbox)
        
        # Mod信息
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        # Mod名称
        name_label = QLabel(self.mod_name)
        name_font = QFont()
        name_font.setPointSize(12)
        name_font.setBold(True)
        name_label.setFont(name_font)
        name_label.setStyleSheet("color: #ffffff;")
        info_layout.addWidget(name_label)
        
        # Mod路径
        path_label = QLabel(f"路径: {self.mod_path}")
        path_label.setStyleSheet("color: #cccccc; font-size: 10px;")
        info_layout.addWidget(path_label)
        
        layout.addLayout(info_layout)
        
        # 弹性空间
        layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        
    def is_enabled(self) -> bool:
        """获取启用状态"""
        return self.checkbox.isChecked()
        
    def set_enabled(self, enabled: bool):
        """设置启用状态"""
        self.checkbox.setChecked(enabled)


class ModConfigPage(QWidget):
    """Mod配置页面"""
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        self.config = ConfigManager()
        self.mod_items = {}
        
        self._init_ui()
        self._load_mods()
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("Mod配置")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 操作按钮区域
        buttons_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("🔄 刷新Mod列表")
        self.refresh_btn.clicked.connect(self._load_mods)
        buttons_layout.addWidget(self.refresh_btn)
        
        self.select_all_btn = QPushButton("✅ 全选")
        self.select_all_btn.clicked.connect(self._select_all_mods)
        buttons_layout.addWidget(self.select_all_btn)
        
        self.deselect_all_btn = QPushButton("❌ 全不选")
        self.deselect_all_btn.clicked.connect(self._deselect_all_mods)
        buttons_layout.addWidget(self.deselect_all_btn)
        
        self.apply_btn = QPushButton("💾 应用配置")
        self.apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        self.apply_btn.clicked.connect(self._apply_config)
        buttons_layout.addWidget(self.apply_btn)
        
        buttons_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        layout.addLayout(buttons_layout)
        
        # Mod列表区域
        mods_group = QGroupBox("可用Mod列表")
        mods_layout = QVBoxLayout(mods_group)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarNever)
        
        self.mods_widget = QWidget()
        self.mods_layout = QVBoxLayout(self.mods_widget)
        self.mods_layout.setSpacing(5)
        
        scroll_area.setWidget(self.mods_widget)
        mods_layout.addWidget(scroll_area)
        
        layout.addWidget(mods_group)
        
        # 配置预览区域
        preview_group = QGroupBox("当前配置预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.config_preview = QTextEdit()
        self.config_preview.setMaximumHeight(200)
        self.config_preview.setReadOnly(True)
        self.config_preview.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 11px;
            }
        """)
        preview_layout.addWidget(self.config_preview)
        
        layout.addWidget(preview_group)
        
    def _load_mods(self):
        """加载Mod列表"""
        try:
            # 清空现有Mod项目
            for item in self.mod_items.values():
                item.setParent(None)
            self.mod_items.clear()
            
            # 扫描Mods文件夹
            mods_dir = Path("Mods")
            if not mods_dir.exists():
                self.logger.warning("Mods文件夹不存在")
                return
                
            enabled_mods = self.config.get('mods.enabled', [])
            
            for mod_path in mods_dir.iterdir():
                if mod_path.is_dir() and mod_path.name not in ['current.me3']:
                    mod_id = mod_path.name
                    mod_name = mod_id.replace('_', ' ').title()
                    
                    # 检查是否启用
                    enabled = mod_id in enabled_mods
                    
                    # 创建Mod项目
                    mod_item = ModItem(mod_id, mod_name, str(mod_path), enabled)
                    mod_item.mod_toggled.connect(self._on_mod_toggled)
                    
                    self.mods_layout.addWidget(mod_item)
                    self.mod_items[mod_id] = mod_item
                    
            # 添加弹性空间
            self.mods_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
            
            # 更新配置预览
            self._update_config_preview()
            
            self.logger.info(f"加载了 {len(self.mod_items)} 个Mod")
            
        except Exception as e:
            self.logger.error(f"加载Mod列表失败: {e}")
            QMessageBox.critical(self, "错误", f"加载Mod列表失败: {e}")
            
    def _on_mod_toggled(self, mod_id: str, enabled: bool):
        """Mod启用状态改变"""
        self._update_config_preview()
        
    def _select_all_mods(self):
        """全选Mod"""
        for mod_item in self.mod_items.values():
            mod_item.set_enabled(True)
        self._update_config_preview()
        
    def _deselect_all_mods(self):
        """全不选Mod"""
        for mod_item in self.mod_items.values():
            mod_item.set_enabled(False)
        self._update_config_preview()
        
    def _update_config_preview(self):
        """更新配置预览"""
        try:
            config_data = self._generate_config()
            config_text = toml.dumps(config_data)
            self.config_preview.setPlainText(config_text)
        except Exception as e:
            self.logger.error(f"更新配置预览失败: {e}")
            self.config_preview.setPlainText(f"配置预览错误: {e}")
            
    def _generate_config(self) -> dict:
        """生成ME3配置"""
        config = {
            "profileVersion": "v1",
            "packages": [],
            "natives": []
        }
        
        # 添加启用的Mod包
        for mod_id, mod_item in self.mod_items.items():
            if mod_item.is_enabled():
                package = {
                    "id": mod_id,
                    "source": f"{mod_id}/"
                }
                
                # 检查是否有依赖关系
                if mod_id == "Unlocks":
                    package["load_after"] = [
                        {"id": "Ascension", "optional": True},
                        {"id": "randomizer", "optional": True}
                    ]
                    
                config["packages"].append(package)
                
        # 添加原生库（如果存在）
        natives_path = Path("Mods/SeamlessCoop/nrsc.dll")
        if natives_path.exists():
            config["natives"].append({
                "path": "SeamlessCoop/nrsc.dll"
            })
            
        return config
        
    def _apply_config(self):
        """应用配置"""
        try:
            # 生成配置
            config_data = self._generate_config()
            
            # 保存到current.me3文件
            config_file = Path("Mods/current.me3")
            config_file.parent.mkdir(exist_ok=True)
            
            with open(config_file, 'w', encoding='utf-8') as f:
                toml.dump(config_data, f)
                
            # 保存启用的Mod列表到应用配置
            enabled_mods = [mod_id for mod_id, mod_item in self.mod_items.items() 
                           if mod_item.is_enabled()]
            self.config.set('mods.enabled', enabled_mods)
            
            self.logger.info(f"Mod配置已应用，启用了 {len(enabled_mods)} 个Mod")
            QMessageBox.information(self, "成功", f"Mod配置已应用\n启用了 {len(enabled_mods)} 个Mod")
            
        except Exception as e:
            self.logger.error(f"应用Mod配置失败: {e}")
            QMessageBox.critical(self, "错误", f"应用Mod配置失败: {e}")
