#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏管理器 - 负责游戏路径配置、破解管理等功能
"""

import shutil
from pathlib import Path
from typing import List, Optional
from PySide6.QtCore import QObject, Signal

from ..utils.logger import Logger
from ..utils.config import ConfigManager


class GameManager(QObject):
    """游戏管理器"""
    
    # 信号定义
    game_path_changed = Signal(str)  # 游戏路径改变
    crack_status_changed = Signal(bool)  # 破解状态改变
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        self.config = ConfigManager()
        
        # 在启动时验证配置
        self._validate_startup_config()
        
    def _validate_startup_config(self):
        """启动时验证配置"""
        try:
            # 验证gconfig.ini中的游戏路径
            gconfig_path = Path("OnlineFix/gconfig.ini")
            if gconfig_path.exists():
                with open(gconfig_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if content:
                        # 移除引号
                        game_path = content.strip('"\'')
                        
                        if not self.validate_game_path(game_path):
                            self.logger.warning(f"gconfig.ini中的游戏路径无效: {game_path}")
                            # 清空无效路径
                            self.config.set('game.path', '')
                        else:
                            # 同步到应用配置
                            self.config.set('game.path', game_path)
                            self.logger.info(f"已加载游戏路径: {game_path}")
                            
        except Exception as e:
            self.logger.error(f"验证启动配置失败: {e}")
            
    def validate_game_path(self, path: str) -> bool:
        """验证游戏路径是否有效"""
        if not path:
            return False
            
        path_obj = Path(path)
        
        # 检查文件是否存在
        if not path_obj.exists():
            return False
            
        # 检查是否是nightreign.exe文件
        if path_obj.name.lower() != "nightreign.exe":
            return False
            
        return True
        
    def set_game_path(self, path: str) -> bool:
        """设置游戏路径"""
        try:
            if not self.validate_game_path(path):
                raise ValueError("无效的游戏路径")
                
            # 保存到应用配置
            self.config.set('game.path', path)
            
            # 保存到gconfig.ini
            self._save_to_gconfig(path)
            
            self.logger.info(f"游戏路径已设置: {path}")
            self.game_path_changed.emit(path)
            
            return True
            
        except Exception as e:
            self.logger.error(f"设置游戏路径失败: {e}")
            return False
            
    def _save_to_gconfig(self, path: str):
        """保存路径到gconfig.ini"""
        try:
            gconfig_path = Path("OnlineFix/gconfig.ini")
            gconfig_path.parent.mkdir(exist_ok=True)
            
            with open(gconfig_path, 'w', encoding='utf-8') as f:
                f.write(f'"{path}"\n')
                
        except Exception as e:
            self.logger.error(f"保存到gconfig.ini失败: {e}")
            raise
            
    def get_game_path(self) -> str:
        """获取当前游戏路径"""
        return self.config.get('game.path', '')
        
    def get_game_directory(self) -> Optional[Path]:
        """获取游戏目录"""
        game_path = self.get_game_path()
        if game_path and self.validate_game_path(game_path):
            return Path(game_path).parent
        return None
        
    def is_game_configured(self) -> bool:
        """检查游戏是否已配置"""
        game_path = self.get_game_path()
        return bool(game_path and self.validate_game_path(game_path))
        
    def get_onlinefix_files(self) -> List[Path]:
        """获取OnlineFix文件列表（排除gconfig.ini）"""
        onlinefix_dir = Path("OnlineFix")
        if not onlinefix_dir.exists():
            return []
            
        files = []
        for file_path in onlinefix_dir.iterdir():
            if file_path.is_file() and file_path.name != "gconfig.ini":
                files.append(file_path)
                
        return files
        
    def apply_crack(self) -> bool:
        """应用破解"""
        try:
            game_dir = self.get_game_directory()
            if not game_dir:
                raise ValueError("游戏目录无效")
                
            onlinefix_files = self.get_onlinefix_files()
            if not onlinefix_files:
                raise ValueError("OnlineFix文件不存在")
                
            copied_files = []
            
            for file_path in onlinefix_files:
                target_path = game_dir / file_path.name
                
                # 备份原文件
                if target_path.exists():
                    backup_path = target_path.with_suffix(target_path.suffix + ".backup")
                    if not backup_path.exists():  # 避免重复备份
                        shutil.copy2(target_path, backup_path)
                        self.logger.info(f"备份文件: {target_path} -> {backup_path}")
                        
                # 复制文件
                shutil.copy2(file_path, target_path)
                copied_files.append(file_path.name)
                self.logger.info(f"复制文件: {file_path} -> {target_path}")
                
            # 更新破解状态
            self.config.set('game.cracked', True)
            self.crack_status_changed.emit(True)
            
            self.logger.info(f"破解已应用，复制了 {len(copied_files)} 个文件: {copied_files}")
            return True
            
        except Exception as e:
            self.logger.error(f"应用破解失败: {e}")
            return False
            
    def remove_crack(self) -> bool:
        """移除破解"""
        try:
            game_dir = self.get_game_directory()
            if not game_dir:
                raise ValueError("游戏目录无效")
                
            onlinefix_files = self.get_onlinefix_files()
            removed_files = []
            
            for file_path in onlinefix_files:
                target_path = game_dir / file_path.name
                
                if target_path.exists():
                    # 删除破解文件
                    target_path.unlink()
                    removed_files.append(file_path.name)
                    self.logger.info(f"删除文件: {target_path}")
                    
                    # 恢复备份文件
                    backup_path = target_path.with_suffix(target_path.suffix + ".backup")
                    if backup_path.exists():
                        shutil.move(backup_path, target_path)
                        self.logger.info(f"恢复备份: {backup_path} -> {target_path}")
                        
            # 更新破解状态
            self.config.set('game.cracked', False)
            self.crack_status_changed.emit(False)
            
            self.logger.info(f"破解已移除，删除了 {len(removed_files)} 个文件: {removed_files}")
            return True
            
        except Exception as e:
            self.logger.error(f"移除破解失败: {e}")
            return False
            
    def is_cracked(self) -> bool:
        """检查是否已破解"""
        return self.config.get('game.cracked', False)
        
    def check_crack_status(self) -> bool:
        """检查实际的破解状态（通过文件存在性）"""
        try:
            game_dir = self.get_game_directory()
            if not game_dir:
                return False
                
            onlinefix_files = self.get_onlinefix_files()
            if not onlinefix_files:
                return False
                
            # 检查是否所有破解文件都存在
            for file_path in onlinefix_files:
                target_path = game_dir / file_path.name
                if not target_path.exists():
                    return False
                    
            return True
            
        except Exception as e:
            self.logger.error(f"检查破解状态失败: {e}")
            return False
            
    def sync_crack_status(self):
        """同步破解状态（配置与实际文件状态）"""
        try:
            actual_status = self.check_crack_status()
            config_status = self.is_cracked()
            
            if actual_status != config_status:
                self.logger.info(f"同步破解状态: {config_status} -> {actual_status}")
                self.config.set('game.cracked', actual_status)
                self.crack_status_changed.emit(actual_status)
                
        except Exception as e:
            self.logger.error(f"同步破解状态失败: {e}")
            
    def get_game_info(self) -> dict:
        """获取游戏信息"""
        game_path = self.get_game_path()
        
        info = {
            'path': game_path,
            'configured': self.is_game_configured(),
            'cracked': self.is_cracked(),
            'directory': str(self.get_game_directory()) if self.get_game_directory() else '',
            'onlinefix_files_count': len(self.get_onlinefix_files())
        }
        
        return info
