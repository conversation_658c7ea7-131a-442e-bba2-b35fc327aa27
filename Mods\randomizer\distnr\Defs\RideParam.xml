<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>RIDE_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u32 atkChrId">
      <DisplayName>乗る側キャラID</DisplayName>
      <Description>騎乗時に「乗る側」のキャラクタを特定するパラメータです</Description>
      <Maximum>10000</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="u32 defChrId">
      <DisplayName>乗られる側キャラID</DisplayName>
      <Description>騎乗時に「乗られる側」のキャラクタを特定するパラメータです</Description>
      <Maximum>10000</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="s32 rideCamParamId = -1">
      <DisplayName>騎乗時カメラID</DisplayName>
      <Description>騎乗時のカメラパラメータを専用のカメラに変更するパラメータです。PC専用のパラメータで、敵に設定しても機能しません。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="u32 atkChrAnimId">
      <DisplayName>乗る側_騎乗アニメの種類と方向変数値</DisplayName>
      <Description>キャラクタアニメ再生を制御しているツール「HavokAnimationTool（HAT）」の「RideOn（騎乗時再生）ステート」内に設定しているvariable（変数）"RideOnAnimId"の値を書き換えるパラメータです</Description>
      <Maximum>10000</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="u32 defChrAnimId">
      <DisplayName>乗られる側_騎乗アニメの種類と方向変数値</DisplayName>
      <Description>キャラクタアニメ再生を制御しているツール「HavokAnimationTool（HAT）」の「RiddenOn（被騎乗時再生）ステート」内に設定しているvariable（変数）"RiddenOnAnimId"の値を書き換えるパラメータです</Description>
      <Maximum>10000</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="s32 defAdjustDmyId = -1">
      <DisplayName>騎乗アニメ開始時の乗られる側の位置合わせダミポリID</DisplayName>
      <Description>乗られる側にのみ必要なダミポリ設定です（乗る側キャラクタには、設定の必要ありません）</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="s32 defCheckDmyId = -1">
      <DisplayName>騎乗判定_乗られる側の判定基準ダミポリID</DisplayName>
      <Description>乗る側のキャラには、ダミポリの設定はとくに必要ありません</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="f32 diffAngMyToDef">
      <DisplayName>騎乗判定_乗る側の正面判定角度範囲[deg]</DisplayName>
      <Description>乗る側の【向き】と、乗られる側の「正面判定ダミポリID」の角度差で判定します。値が大きいほど、「そっぽを向いていても乗れる」ようになります</Description>
      <Minimum>0</Minimum>
      <Maximum>180</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="f32 dist">
      <DisplayName>騎乗判定_有効距離[m]</DisplayName>
      <Description>「対象との騎乗可能距離」を決定するパラメータです</Description>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1800</SortID>
    </Field>
    <Field Def="f32 upperYRange">
      <DisplayName>騎乗判定_有効高さ上方向[m]</DisplayName>
      <Description>乗られる対象が、乗る対象よりどの程度上方にいても騎乗可能か？を、メートル単位で設定します</Description>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>1900</SortID>
    </Field>
    <Field Def="f32 lowerYRange">
      <DisplayName>騎乗判定_有効高さ下方向[m]</DisplayName>
      <Description>乗られる対象が、乗る対象よりどの程度下方にいても騎乗可能か？を、メートル単位で設定します</Description>
      <Minimum>0</Minimum>
      <Maximum>10000</Maximum>
      <Increment>0.1</Increment>
      <SortID>2000</SortID>
    </Field>
    <Field Def="f32 diffAngMin">
      <DisplayName>騎乗判定_対象間の角度差範囲min[deg]</DisplayName>
      <Description>乗る側が、乗られる側のどの範囲(角度)にいれば騎乗できるか？の最小値を設定します</Description>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="f32 diffAngMax">
      <DisplayName>騎乗判定_対象間の角度差範囲max[deg]</DisplayName>
      <Description>乗る側が、乗られる側のどの範囲(角度)にいれば騎乗できるか？の最大値を設定します</Description>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="dummy8 pad[12]">
      <DisplayName>予約</DisplayName>
      <Description>予約領域</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>2201</SortID>
    </Field>
  </Fields>
</PARAMDEF>