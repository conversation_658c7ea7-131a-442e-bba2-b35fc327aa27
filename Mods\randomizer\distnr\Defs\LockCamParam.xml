<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>LOCK_CAM_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 camDistTarget = 4">
      <DisplayName>カメラ距離目標[m]</DisplayName>
      <Description>カメラ距離目標</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0.1</Minimum>
      <Maximum>100</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="f32 rotRangeMinX = -40">
      <DisplayName>X軸回転最小値[deg]</DisplayName>
      <Description>X軸回転最小値</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-80</Minimum>
      <Maximum>80</Maximum>
      <Increment>0.1</Increment>
      <SortID>200</SortID>
    </Field>
    <Field Def="f32 lockRotXShiftRatio = 0.6">
      <DisplayName>ロックX回転シフト率(0.0～1.0)</DisplayName>
      <Description>ロックX回転シフト率</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="f32 chrOrgOffset_Y = 1.42">
      <DisplayName>キャラ基点オフセット(キャラ空間)</DisplayName>
      <Description>キャラ基点オフセット</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-10</Minimum>
      <Maximum>10</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="f32 chrLockRangeMaxRadius = 15">
      <DisplayName>キャラ範囲最大半径[m]</DisplayName>
      <Description>キャラ範囲最大半径</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="f32 camFovY = 43">
      <DisplayName>縦画角[deg]</DisplayName>
      <Description>縦画角</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>180</Maximum>
      <Increment>0.1</Increment>
      <SortID>600</SortID>
    </Field>
    <Field Def="f32 chrLockRangeMaxRadius_forD = -1">
      <DisplayName>暗闇用キャラ範囲最大半径[m]</DisplayName>
      <Description>暗いところでのキャラのロック可能範囲</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>510</SortID>
    </Field>
    <Field Def="f32 chrLockRangeMaxRadius_forPD = -1">
      <DisplayName>真っ暗闇用キャラ範囲最大半径[m]</DisplayName>
      <Description>真っ暗闇でのキャラのロック可能範囲</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99</Maximum>
      <SortID>520</SortID>
    </Field>
    <Field Def="f32 closeMaxHeight">
      <DisplayName>近接攻撃自動捕捉 上限高さ[m]</DisplayName>
      <Description>非ロックオン時の自動ロックオン判定高さ上限　近接</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="f32 closeMinHeight">
      <DisplayName>近接攻撃自動捕捉 下限高さ[m]</DisplayName>
      <Description>非ロックオン時の自動ロックオン判定高さ下限　近接</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="f32 closeAngRange">
      <DisplayName>近接攻撃自動捕捉 角度範囲 左右[deg]</DisplayName>
      <Description>非ロックオン時の自動ロックオン判定左右角度[deg]　近接</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>180</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="f32 closeMaxRadius">
      <DisplayName>近接攻撃自動捕捉 キャラ範囲最大半径[m]</DisplayName>
      <Description>非ロックオン時の自動ロックオン判定距離　近接</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="f32 closeMaxRadius_forD">
      <DisplayName>近接攻撃自動捕捉 暗闇用キャラ範囲最大半径[m]</DisplayName>
      <Description>非ロックオン時の自動ロックオン判定距離_暗闇　近接</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="f32 closeMaxRadius_forPD">
      <DisplayName>近接攻撃自動捕捉 真っ暗闇用キャラ範囲最大半径[m]</DisplayName>
      <Description>非ロックオン時の自動ロックオン_真っ暗　近接</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="f32 bulletMaxRadius">
      <DisplayName>弾丸自動捕捉 キャラ範囲最大半径[m]</DisplayName>
      <Description>非ロックオン時の自動ロックオン判定距離　弾丸</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="f32 bulletMaxRadius_forD">
      <DisplayName>弾丸自動捕捉 暗闇用キャラ範囲最大半径[m]</DisplayName>
      <Description>非ロックオン時の自動ロックオン判定距離_暗闇　弾丸</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="f32 bulletMaxRadius_forPD">
      <DisplayName>弾丸自動捕捉 真っ暗闇用キャラ範囲最大半径[m]</DisplayName>
      <Description>非ロックオン時の自動ロックオン判定距離_真っ暗　弾丸</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="f32 bulletAngRange">
      <DisplayName>弾丸自動捕捉 角度範囲 左右[deg]</DisplayName>
      <Description>非ロックオン時の自動ロックオン左右角度　弾丸</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>180</Maximum>
      <SortID>1250</SortID>
    </Field>
    <Field Def="f32 lockTgtKeepTime = 2">
      <DisplayName>ロック条件を満たさなくてもロック継続する時間[s]</DisplayName>
      <Description>ロック条件を満たさなくてもロック継続する時間(0.0で無効)</Description>
      <DisplayFormat>%0.2f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="f32 chrTransChaseRateForNormal = -1">
      <DisplayName>通常用キャラ並進追尾率</DisplayName>
      <Description>通常用キャラ並進追尾率(-1.0で無効)</Description>
      <DisplayFormat>%0.3f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>1</Maximum>
      <SortID>450</SortID>
    </Field>
    <Field Def="dummy8 pad[48]">
      <DisplayName>パディング</DisplayName>
      <DisplayFormat>%0.3f</DisplayFormat>
      <SortID>2001</SortID>
    </Field>
  </Fields>
</PARAMDEF>