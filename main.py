#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ME3 Mod Manager - 主入口文件
现代化的PySide6 GUI应用程序，用于管理ME3 mod和游戏配置
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from src.core.app import ModManagerApp

def main():
    """应用程序主入口"""
    try:
        app = ModManagerApp(sys.argv)
        return app.run()
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
