<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>ATK_PARAM_ST</ParamType>
  <DataVersion>4</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 hit0_Radius">
      <DisplayName>あたり0 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>4000</SortID>
    </Field>
    <Field Def="f32 hit1_Radius">
      <DisplayName>あたり1 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>4500</SortID>
    </Field>
    <Field Def="f32 hit2_Radius">
      <DisplayName>あたり2 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5000</SortID>
    </Field>
    <Field Def="f32 hit3_Radius">
      <DisplayName>あたり3 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5500</SortID>
    </Field>
    <Field Def="f32 knockbackDist">
      <DisplayName>ノックバック距離[m]</DisplayName>
      <Description>ノックバック距離[m]</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <SortID>5790</SortID>
    </Field>
    <Field Def="f32 hitStopTime">
      <DisplayName>ヒットストップ時間[s]</DisplayName>
      <Description>ヒットストップの停止時間[s]</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <SortID>6110</SortID>
    </Field>
    <Field Def="s32 spEffectId0 = -1">
      <DisplayName>特殊効果0</DisplayName>
      <Description>特殊効果パラメータで作成したＩＤを入れる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6300</SortID>
    </Field>
    <Field Def="s32 spEffectId1 = -1">
      <DisplayName>特殊効果1</DisplayName>
      <Description>特殊効果パラメータで作成したＩＤを入れる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6400</SortID>
    </Field>
    <Field Def="s32 spEffectId2 = -1">
      <DisplayName>特殊効果2</DisplayName>
      <Description>特殊効果パラメータで作成したＩＤを入れる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6500</SortID>
    </Field>
    <Field Def="s32 spEffectId3 = -1">
      <DisplayName>特殊効果3</DisplayName>
      <Description>特殊効果パラメータで作成したＩＤを入れる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6600</SortID>
    </Field>
    <Field Def="s32 spEffectId4 = -1">
      <DisplayName>特殊効果4</DisplayName>
      <Description>特殊効果パラメータで作成したＩＤを入れる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6700</SortID>
    </Field>
    <Field Def="s16 hit0_DmyPoly1">
      <DisplayName>あたり0 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>3800</SortID>
    </Field>
    <Field Def="s16 hit1_DmyPoly1">
      <DisplayName>あたり1 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>4300</SortID>
    </Field>
    <Field Def="s16 hit2_DmyPoly1">
      <DisplayName>あたり2 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>4800</SortID>
    </Field>
    <Field Def="s16 hit3_DmyPoly1">
      <DisplayName>あたり3 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5300</SortID>
    </Field>
    <Field Def="s16 hit0_DmyPoly2">
      <DisplayName>あたり0 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>3900</SortID>
    </Field>
    <Field Def="s16 hit1_DmyPoly2">
      <DisplayName>あたり1 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>4400</SortID>
    </Field>
    <Field Def="s16 hit2_DmyPoly2">
      <DisplayName>あたり2 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>4900</SortID>
    </Field>
    <Field Def="s16 hit3_DmyPoly2">
      <DisplayName>あたり3 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5400</SortID>
    </Field>
    <Field Def="u16 blowingCorrection">
      <DisplayName>吹き飛ばし補正値</DisplayName>
      <Description>吹き飛ばす時の補正値</Description>
      <Maximum>60000</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="u16 atkPhysCorrection">
      <DisplayName>物理攻撃力補正値</DisplayName>
      <Description>PCのみ。物理攻撃力基本値に掛ける倍率</Description>
      <Maximum>60000</Maximum>
      <SortID>2300</SortID>
    </Field>
    <Field Def="u16 atkMagCorrection">
      <DisplayName>魔法攻撃力補正値</DisplayName>
      <Description>PCのみ。魔法攻撃力に掛ける倍率（弓の場合は、飛び道具を補正）</Description>
      <Maximum>60000</Maximum>
      <SortID>2400</SortID>
    </Field>
    <Field Def="u16 atkFireCorrection">
      <DisplayName>炎攻撃力補正値</DisplayName>
      <Description>PCのみ。炎攻撃力に掛ける倍率（弓の場合は、飛び道具を補正）</Description>
      <Maximum>60000</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="u16 atkThunCorrection">
      <DisplayName>電撃攻撃力補正値</DisplayName>
      <Description>PCのみ。電撃攻撃力に掛ける倍率（弓の場合は、飛び道具を補正）</Description>
      <Maximum>60000</Maximum>
      <SortID>2510</SortID>
    </Field>
    <Field Def="u16 atkStamCorrection">
      <DisplayName>スタミナ攻撃力補正値</DisplayName>
      <Description>PCのみ。スタミナ攻撃力に掛ける倍率</Description>
      <Maximum>60000</Maximum>
      <SortID>2600</SortID>
    </Field>
    <Field Def="u16 guardAtkRateCorrection">
      <DisplayName>はじき攻撃力補正値</DisplayName>
      <Description>PCのみ。1のみ</Description>
      <Maximum>60000</Maximum>
      <SortID>2900</SortID>
    </Field>
    <Field Def="u16 guardBreakCorrection">
      <DisplayName>はじき防御力補正値</DisplayName>
      <Description>PCのみ。攻撃のはじかれ基本値に掛ける倍率</Description>
      <Maximum>60000</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="u16 atkThrowEscapeCorrection">
      <DisplayName>投げ抜け攻撃力補正値</DisplayName>
      <Description>投げ抜け攻撃に対する武器補正値</Description>
      <Maximum>60000</Maximum>
      <SortID>3630</SortID>
    </Field>
    <Field Def="u8 subCategory1">
      <DisplayName>サブカテゴリ1</DisplayName>
      <Enum>ATK_SUB_CATEGORY</Enum>
      <Description>サブカテゴリ1</Description>
      <SortID>3605</SortID>
    </Field>
    <Field Def="u8 subCategory2">
      <DisplayName>サブカテゴリ2</DisplayName>
      <Enum>ATK_SUB_CATEGORY</Enum>
      <Description>サブカテゴリ2</Description>
      <SortID>3606</SortID>
    </Field>
    <Field Def="u16 atkPhys">
      <DisplayName>物理攻撃力</DisplayName>
      <Description>NPCのみ。物理攻撃の基本ダメージ</Description>
      <Maximum>9999</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="u16 atkMag">
      <DisplayName>魔法攻撃力</DisplayName>
      <Description>NPCのみ。魔法攻撃の追加ダメージ</Description>
      <Maximum>9999</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="u16 atkFire">
      <DisplayName>炎攻撃力</DisplayName>
      <Description>NPCのみ。炎攻撃の追加ダメージ</Description>
      <Maximum>9999</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="u16 atkThun">
      <DisplayName>電撃攻撃力</DisplayName>
      <Description>NPCのみ。電撃攻撃の追加ダメージ</Description>
      <Maximum>9999</Maximum>
      <SortID>810</SortID>
    </Field>
    <Field Def="u16 atkStam">
      <DisplayName>スタミナ攻撃力</DisplayName>
      <Description>NPCのみ。敵（プレイヤー）のスタミナに対するダメージ量</Description>
      <Maximum>999</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="u16 guardAtkRate">
      <DisplayName>はじき攻撃力</DisplayName>
      <Description>NPCのみ。はじき値</Description>
      <Maximum>999</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="u16 guardBreakRate">
      <DisplayName>はじき防御力</DisplayName>
      <Description>NPCのみ。攻撃がはじかれるかどうかの判定に利用する値</Description>
      <Maximum>999</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="dummy8 pad6[1]">
      <DisplayName>pad</DisplayName>
      <SortID>12001</SortID>
    </Field>
    <Field Def="u8 isEnableCalcDamageForBushesObj">
      <DisplayName>茂みにダメージ可</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>「茂みダメージで壊れるか」ONのアセットに対してダメージ計算をするか？を設定します。〇：計算する、×：計算しない(つまりダメージをあたえることはできない)【GR】SEQ20617 </Description>
      <Maximum>1</Maximum>
      <SortID>2201</SortID>
    </Field>
    <Field Def="u16 atkThrowEscape">
      <DisplayName>投げ抜け攻撃力</DisplayName>
      <Description>投げ抜け攻撃力</Description>
      <Maximum>999</Maximum>
      <SortID>3640</SortID>
    </Field>
    <Field Def="u16 atkObj">
      <DisplayName>オブジェ攻撃力</DisplayName>
      <Description>ＯＢＪに対する攻撃力</Description>
      <Maximum>999</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="s16 guardStaminaCutRate">
      <DisplayName>ガード時スタミナカット率補正</DisplayName>
      <Description>武器パラメータ、ＮＰＣパラメータに設定されている【ガード時スタミナカット率】を補正する</Description>
      <Minimum>-100</Minimum>
      <Maximum>9999</Maximum>
      <SortID>3200</SortID>
    </Field>
    <Field Def="s16 guardRate">
      <DisplayName>ガード倍率</DisplayName>
      <Description>ＮＰＣ、武器パラメータで設定してあるガード性能を一律で補正を掛ける0で、1倍／100で、2倍／－100で、0　にパラメータが増減するようにするガード倍率　=　（ガード倍率/100　+　1）</Description>
      <Minimum>-100</Minimum>
      <Maximum>999</Maximum>
      <SortID>3500</SortID>
    </Field>
    <Field Def="u16 throwTypeId">
      <DisplayName>投げタイプID</DisplayName>
      <Description>投げパラメータと紐付けされているID</Description>
      <Maximum>9999</Maximum>
      <SortID>3620</SortID>
    </Field>
    <Field Def="u8 hit0_hitType">
      <DisplayName>あたり0 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>4100</SortID>
    </Field>
    <Field Def="u8 hit1_hitType">
      <DisplayName>あたり1 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>4600</SortID>
    </Field>
    <Field Def="u8 hit2_hitType">
      <DisplayName>あたり2 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5100</SortID>
    </Field>
    <Field Def="u8 hit3_hitType">
      <DisplayName>あたり3 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5600</SortID>
    </Field>
    <Field Def="u8 hti0_Priority">
      <DisplayName>あたり0 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>4200</SortID>
    </Field>
    <Field Def="u8 hti1_Priority">
      <DisplayName>あたり1 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>4700</SortID>
    </Field>
    <Field Def="u8 hti2_Priority">
      <DisplayName>あたり2 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5200</SortID>
    </Field>
    <Field Def="u8 hti3_Priority">
      <DisplayName>あたり3 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5700</SortID>
    </Field>
    <Field Def="u8 dmgLevel">
      <DisplayName>ダメージレベル</DisplayName>
      <Description>攻撃したとき、敵にどのダメージモーションを再生するか？を決める.</Description>
      <Maximum>100</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="u8 mapHitType">
      <DisplayName>マップあたり参照</DisplayName>
      <Enum>ATK_PARAM_MAP_HIT</Enum>
      <Description>攻撃あたりが、どのマップあたりを見るか？を設定</Description>
      <SortID>300</SortID>
    </Field>
    <Field Def="s8 guardCutCancelRate">
      <DisplayName>ガードカット率無効化倍率</DisplayName>
      <Description>ガードカット率無効化倍率（－100～100）　→0のとき通常／－100で完全無効化／100で相手の防御効果倍増 　→－50とすれば、100％カットの盾が、50％カットになります </Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="u8 atkAttribute">
      <DisplayName>物理属性</DisplayName>
      <Enum>ATKPARAM_ATKATTR_TYPE</Enum>
      <Description>攻撃に設定する物理属性</Description>
      <SortID>1510</SortID>
    </Field>
    <Field Def="u8 spAttribute">
      <DisplayName>特殊属性</DisplayName>
      <Enum>ATKPARAM_SPATTR_TYPE</Enum>
      <Description>攻撃に設定する特殊属性</Description>
      <SortID>1520</SortID>
    </Field>
    <Field Def="u8 atkType">
      <DisplayName>攻撃属性[SFX/SE]</DisplayName>
      <Enum>BEHAVIOR_ATK_TYPE</Enum>
      <Description>攻撃時のSFX/SEを指定(属性、材質、サイズで1セット)</Description>
      <SortID>1600</SortID>
    </Field>
    <Field Def="u8 atkMaterial">
      <DisplayName>攻撃材質[SFX/SE]</DisplayName>
      <Enum>WEP_MATERIAL_ATK</Enum>
      <Description>攻撃時のSFX/SEを指定(属性、材質、サイズで1セット)</Description>
      <SortID>1700</SortID>
    </Field>
    <Field Def="u8 guardRangeType">
      <DisplayName>ガード判定位置</DisplayName>
      <Enum>ATKPARAM_GUARD_RANGE_TYPE</Enum>
      <Description>ガード判定位置</Description>
      <Maximum>1</Maximum>
      <SortID>3350</SortID>
    </Field>
    <Field Def="u16 defSeMaterial1">
      <DisplayName>防御材質1[SE]</DisplayName>
      <Enum>WEP_MATERIAL_DEF</Enum>
      <Description>ガード時のSEに使用1</Description>
      <Maximum>9999</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="u8 hitSourceType">
      <DisplayName>あたり発生源</DisplayName>
      <Enum>ATK_PARAM_HIT_SOURCE</Enum>
      <Description>攻撃あたりのダミポリＩＤをどこから取ってくるか？を指定する</Description>
      <SortID>3700</SortID>
    </Field>
    <Field Def="u8 throwFlag">
      <DisplayName>投げ</DisplayName>
      <Enum>ATK_PATAM_THROWFLAG_TYPE</Enum>
      <Description>投げ情報に用いるフラグ</Description>
      <SortID>3610</SortID>
    </Field>
    <Field Def="u8 disableGuard:1">
      <DisplayName>ガード不可フラグ</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>1の場合、ガード側のガードを無視して、ダメージレベルを入れる</Description>
      <Maximum>1</Maximum>
      <SortID>3300</SortID>
    </Field>
    <Field Def="u8 disableStaminaAttack:1">
      <DisplayName>スタミナ減らない</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>スタミナ攻撃力による「崩され判定」は行うが、実際にスタミナは減らさない</Description>
      <Maximum>1</Maximum>
      <SortID>3400</SortID>
    </Field>
    <Field Def="u8 disableHitSpEffect:1">
      <DisplayName>ヒット時特殊効果無効</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>攻撃ヒットしたときの特殊効果を無効にします。SCEバグ対策</Description>
      <Maximum>1</Maximum>
      <SortID>3600</SortID>
    </Field>
    <Field Def="u8 IgnoreNotifyMissSwingForAI:1">
      <DisplayName>AIに空振り通知しない</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>AIに空振り通知しない</Description>
      <Maximum>1</Maximum>
      <SortID>6200</SortID>
    </Field>
    <Field Def="u8 repeatHitSfx:1">
      <DisplayName>ＨＩＴ時にＳＦＸを何度も出すか</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>敵専用：壁Hit時のSFXが連続で発生するか</Description>
      <Maximum>1</Maximum>
      <SortID>2010</SortID>
    </Field>
    <Field Def="u8 isArrowAtk:1">
      <DisplayName>矢攻撃か</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>部位ダメージ判定に使用する。</Description>
      <Maximum>1</Maximum>
      <SortID>310</SortID>
    </Field>
    <Field Def="u8 isGhostAtk:1">
      <DisplayName>霊体攻撃か</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>霊体ダメージ判定に使用。</Description>
      <Maximum>1</Maximum>
      <SortID>320</SortID>
    </Field>
    <Field Def="u8 isDisableNoDamage:1">
      <DisplayName>無敵を貫通するか</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>ステップ等の無敵効果を無視します、TAEの完全無敵は無視できません。</Description>
      <Maximum>1</Maximum>
      <SortID>330</SortID>
    </Field>
    <Field Def="s8 atkPow_forSfx">
      <DisplayName>攻撃強度[SFX]</DisplayName>
      <Enum>ATKPARAM_SFX_ATK_POW</Enum>
      <Description>攻撃強度[SFX]</Description>
      <Minimum>0</Minimum>
      <SortID>1710</SortID>
    </Field>
    <Field Def="s8 atkDir_forSfx">
      <DisplayName>攻撃方向[SFX]</DisplayName>
      <Enum>ATKPARAM_SFX_ATK_DIR</Enum>
      <Description>攻撃方向[SFX]</Description>
      <Minimum>0</Minimum>
      <SortID>1720</SortID>
    </Field>
    <Field Def="u8 opposeTarget:1 = 1">
      <DisplayName>対象：●敵対</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>対象：●敵対</Description>
      <Maximum>1</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u8 friendlyTarget:1">
      <DisplayName>対象：○味方</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>対象：○味方</Description>
      <Maximum>1</Maximum>
      <SortID>210</SortID>
    </Field>
    <Field Def="u8 selfTarget:1">
      <DisplayName>対象：自分</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>対象：自分</Description>
      <Maximum>1</Maximum>
      <SortID>220</SortID>
    </Field>
    <Field Def="u8 isCheckDoorPenetration:1">
      <DisplayName>扉貫通チェックを行うか</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>扉貫通チェックを行うかどうか。○の場合は扉越しの対象を攻撃できるかどうかの判定を行います。</Description>
      <Maximum>1</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="u8 isVsRideAtk:1">
      <DisplayName>騎乗特攻か</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>騎乗中の騎乗特攻対象に攻撃を当てた場合、SAダメージに倍率補正が掛かる</Description>
      <Maximum>1</Maximum>
      <SortID>325</SortID>
    </Field>
    <Field Def="u8 isAddBaseAtk:1">
      <DisplayName>武器攻撃でも加算攻撃力を参照するか</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>武器攻撃でも加算攻撃力を参照するか</Description>
      <Maximum>1</Maximum>
      <SortID>11000</SortID>
    </Field>
    <Field Def="u8 excludeThreatLvNotify:1">
      <DisplayName>脅威度通知対象除外か</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>脅威度通知対象除外か</Description>
      <Maximum>1</Maximum>
      <SortID>12000</SortID>
    </Field>
    <Field Def="dummy8 pad1:1">
      <SortID>12002</SortID>
    </Field>
    <Field Def="u8 atkBehaviorId">
      <DisplayName>Behavior用識別値1</DisplayName>
      <Enum>ATKPARAM_BEHAVIOR_ID</Enum>
      <Description>Behavior用識別値：特大ダメージ遷移</Description>
      <SortID>9000</SortID>
    </Field>
    <Field Def="s8 atkPow_forSe">
      <DisplayName>攻撃強度[SE]</DisplayName>
      <Enum>ATKPARAM_SE_ATK_POW</Enum>
      <Description>攻撃強度[SE]</Description>
      <Minimum>0</Minimum>
      <SortID>1711</SortID>
    </Field>
    <Field Def="f32 atkSuperArmor">
      <DisplayName>SA攻撃力</DisplayName>
      <Description>NPCのみ。SAブレイク計算式に利用すする値</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>1310</SortID>
    </Field>
    <Field Def="s32 decalId1 = -1">
      <DisplayName>デカールID1（直接指定）</DisplayName>
      <Description>デカールID1（直接指定）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="s32 decalId2 = -1">
      <DisplayName>デカールID2（直接指定）</DisplayName>
      <Description>デカールID2（直接指定）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2110</SortID>
    </Field>
    <Field Def="s32 AppearAiSoundId">
      <DisplayName>発生時AI音ID</DisplayName>
      <Description>攻撃発生時に発生させるAI音のID</Description>
      <Minimum>0</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>7000</SortID>
    </Field>
    <Field Def="s32 HitAiSoundId">
      <DisplayName>ヒット時AI音ID</DisplayName>
      <Description>ヒット時に発生させるAI音のID</Description>
      <Minimum>0</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>7000</SortID>
    </Field>
    <Field Def="s32 HitRumbleId = -1">
      <DisplayName>ヒット時振動効果(-1無効)</DisplayName>
      <Description>ヒット時の振動ID（-1無効）。次の3つのどれにも当てはまらない時の振動IDとなる</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>6120</SortID>
    </Field>
    <Field Def="s32 HitRumbleIdByNormal = -1">
      <DisplayName>先端ヒット時振動ID</DisplayName>
      <Description>先端にヒットした時のヒット時振動ID（-1無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>6130</SortID>
    </Field>
    <Field Def="s32 HitRumbleIdByMiddle = -1">
      <DisplayName>真ん中ヒット時振動ID</DisplayName>
      <Description>真ん中にヒットした時のヒット時振動ID（-1無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>6140</SortID>
    </Field>
    <Field Def="s32 HitRumbleIdByRoot = -1">
      <DisplayName>根本ヒット時振動ID</DisplayName>
      <Description>根本にヒットした時のヒット時振動ID（-1無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>6150</SortID>
    </Field>
    <Field Def="s32 traceSfxId0 = -1">
      <DisplayName>剣閃SfxID_０</DisplayName>
      <Description>剣閃SfxID_０(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8000</SortID>
    </Field>
    <Field Def="s32 traceDmyIdHead0 = -1">
      <DisplayName>根元剣閃ダミポリID_０</DisplayName>
      <Description>剣閃根元ダミポリID_０(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8001</SortID>
    </Field>
    <Field Def="s32 traceDmyIdTail0 = -1">
      <DisplayName>剣先剣閃ダミポリID_０</DisplayName>
      <Description>剣閃剣先ダミポリID_０</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8002</SortID>
    </Field>
    <Field Def="s32 traceSfxId1 = -1">
      <DisplayName>剣閃SfxID_１</DisplayName>
      <Description>剣閃SfxID_１(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8003</SortID>
    </Field>
    <Field Def="s32 traceDmyIdHead1 = -1">
      <DisplayName>根元剣閃ダミポリID_１</DisplayName>
      <Description>剣閃根元ダミポリID_１(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8004</SortID>
    </Field>
    <Field Def="s32 traceDmyIdTail1 = -1">
      <DisplayName>剣先剣閃ダミポリID_１</DisplayName>
      <Description>剣閃剣先ダミポリID_１</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8005</SortID>
    </Field>
    <Field Def="s32 traceSfxId2 = -1">
      <DisplayName>剣閃SfxID_２</DisplayName>
      <Description>剣閃SfxID_２(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8006</SortID>
    </Field>
    <Field Def="s32 traceDmyIdHead2 = -1">
      <DisplayName>根元剣閃ダミポリID_２</DisplayName>
      <Description>剣閃根元ダミポリID_２(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8007</SortID>
    </Field>
    <Field Def="s32 traceDmyIdTail2 = -1">
      <DisplayName>剣先剣閃ダミポリID_２</DisplayName>
      <Description>剣閃剣先ダミポリID_２</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8008</SortID>
    </Field>
    <Field Def="s32 traceSfxId3 = -1">
      <DisplayName>剣閃SfxID_３</DisplayName>
      <Description>剣閃SfxID_３(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8009</SortID>
    </Field>
    <Field Def="s32 traceDmyIdHead3 = -1">
      <DisplayName>根元剣閃ダミポリID_３</DisplayName>
      <Description>剣閃根元ダミポリID_３(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8010</SortID>
    </Field>
    <Field Def="s32 traceDmyIdTail3 = -1">
      <DisplayName>剣先剣閃ダミポリID_３</DisplayName>
      <Description>剣閃剣先ダミポリID_３</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8011</SortID>
    </Field>
    <Field Def="s32 traceSfxId4 = -1">
      <DisplayName>剣閃SfxID_４</DisplayName>
      <Description>剣閃SfxID_４(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8012</SortID>
    </Field>
    <Field Def="s32 traceDmyIdHead4 = -1">
      <DisplayName>根元剣閃ダミポリID_４</DisplayName>
      <Description>剣閃根元ダミポリID_４(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8013</SortID>
    </Field>
    <Field Def="s32 traceDmyIdTail4 = -1">
      <DisplayName>剣先剣閃ダミポリID_４</DisplayName>
      <Description>剣閃剣先ダミポリID_４</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8014</SortID>
    </Field>
    <Field Def="s32 traceSfxId5 = -1">
      <DisplayName>剣閃SfxID_５</DisplayName>
      <Description>剣閃SfxID_５(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8015</SortID>
    </Field>
    <Field Def="s32 traceDmyIdHead5 = -1">
      <DisplayName>根元剣閃ダミポリID_５</DisplayName>
      <Description>剣閃根元ダミポリID_５(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8016</SortID>
    </Field>
    <Field Def="s32 traceDmyIdTail5 = -1">
      <DisplayName>剣先剣閃ダミポリID_５</DisplayName>
      <Description>剣閃剣先ダミポリID_５</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8017</SortID>
    </Field>
    <Field Def="s32 traceSfxId6 = -1">
      <DisplayName>剣閃SfxID_６</DisplayName>
      <Description>剣閃SfxID_６(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8018</SortID>
    </Field>
    <Field Def="s32 traceDmyIdHead6 = -1">
      <DisplayName>根元剣閃ダミポリID_６</DisplayName>
      <Description>剣閃根元ダミポリID_６(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8019</SortID>
    </Field>
    <Field Def="s32 traceDmyIdTail6 = -1">
      <DisplayName>剣先剣閃ダミポリID_６</DisplayName>
      <Description>剣閃剣先ダミポリID_６</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8020</SortID>
    </Field>
    <Field Def="s32 traceSfxId7 = -1">
      <DisplayName>剣閃SfxID_７</DisplayName>
      <Description>剣閃SfxID_７(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8021</SortID>
    </Field>
    <Field Def="s32 traceDmyIdHead7 = -1">
      <DisplayName>根元剣閃ダミポリID_７</DisplayName>
      <Description>剣閃根元ダミポリID_７(-1無効)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8022</SortID>
    </Field>
    <Field Def="s32 traceDmyIdTail7 = -1">
      <DisplayName>剣先剣閃ダミポリID_７</DisplayName>
      <Description>剣閃剣先ダミポリID_７</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>8023</SortID>
    </Field>
    <Field Def="f32 hit4_Radius">
      <DisplayName>あたり4 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5703</SortID>
    </Field>
    <Field Def="f32 hit5_Radius">
      <DisplayName>あたり5 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5708</SortID>
    </Field>
    <Field Def="f32 hit6_Radius">
      <DisplayName>あたり6 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5713</SortID>
    </Field>
    <Field Def="f32 hit7_Radius">
      <DisplayName>あたり7 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5718</SortID>
    </Field>
    <Field Def="f32 hit8_Radius">
      <DisplayName>あたり8 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5723</SortID>
    </Field>
    <Field Def="f32 hit9_Radius">
      <DisplayName>あたり9 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5728</SortID>
    </Field>
    <Field Def="f32 hit10_Radius">
      <DisplayName>あたり10 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5733</SortID>
    </Field>
    <Field Def="f32 hit11_Radius">
      <DisplayName>あたり11 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5738</SortID>
    </Field>
    <Field Def="f32 hit12_Radius">
      <DisplayName>あたり12 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5743</SortID>
    </Field>
    <Field Def="f32 hit13_Radius">
      <DisplayName>あたり13 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5748</SortID>
    </Field>
    <Field Def="f32 hit14_Radius">
      <DisplayName>あたり14 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5753</SortID>
    </Field>
    <Field Def="f32 hit15_Radius">
      <DisplayName>あたり15 半径</DisplayName>
      <Description>球、カプセルの半径</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5758</SortID>
    </Field>
    <Field Def="s16 hit4_DmyPoly1">
      <DisplayName>あたり4 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5701</SortID>
    </Field>
    <Field Def="s16 hit5_DmyPoly1">
      <DisplayName>あたり5 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5706</SortID>
    </Field>
    <Field Def="s16 hit6_DmyPoly1">
      <DisplayName>あたり6 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5711</SortID>
    </Field>
    <Field Def="s16 hit7_DmyPoly1">
      <DisplayName>あたり7 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5716</SortID>
    </Field>
    <Field Def="s16 hit8_DmyPoly1">
      <DisplayName>あたり8ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5721</SortID>
    </Field>
    <Field Def="s16 hit9_DmyPoly1">
      <DisplayName>あたり9 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5726</SortID>
    </Field>
    <Field Def="s16 hit10_DmyPoly1">
      <DisplayName>あたり10 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5731</SortID>
    </Field>
    <Field Def="s16 hit11_DmyPoly1">
      <DisplayName>あたり11 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5736</SortID>
    </Field>
    <Field Def="s16 hit12_DmyPoly1">
      <DisplayName>あたり12 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5741</SortID>
    </Field>
    <Field Def="s16 hit13_DmyPoly1">
      <DisplayName>あたり13ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5746</SortID>
    </Field>
    <Field Def="s16 hit14_DmyPoly1">
      <DisplayName>あたり14 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5751</SortID>
    </Field>
    <Field Def="s16 hit15_DmyPoly1">
      <DisplayName>あたり15 ダミポリ1</DisplayName>
      <Description>球、カプセル位置のダミポリ</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5756</SortID>
    </Field>
    <Field Def="s16 hit4_DmyPoly2">
      <DisplayName>あたり4 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5702</SortID>
    </Field>
    <Field Def="s16 hit5_DmyPoly2">
      <DisplayName>あたり5ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5707</SortID>
    </Field>
    <Field Def="s16 hit6_DmyPoly2">
      <DisplayName>あたり6ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5712</SortID>
    </Field>
    <Field Def="s16 hit7_DmyPoly2">
      <DisplayName>あたり7ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5717</SortID>
    </Field>
    <Field Def="s16 hit8_DmyPoly2">
      <DisplayName>あたり8 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5722</SortID>
    </Field>
    <Field Def="s16 hit9_DmyPoly2">
      <DisplayName>あたり9ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5727</SortID>
    </Field>
    <Field Def="s16 hit10_DmyPoly2">
      <DisplayName>あたり10 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5732</SortID>
    </Field>
    <Field Def="s16 hit11_DmyPoly2">
      <DisplayName>あたり11 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5737</SortID>
    </Field>
    <Field Def="s16 hit12_DmyPoly2">
      <DisplayName>あたり12 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5742</SortID>
    </Field>
    <Field Def="s16 hit13_DmyPoly2">
      <DisplayName>あたり13 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5747</SortID>
    </Field>
    <Field Def="s16 hit14_DmyPoly2">
      <DisplayName>あたり14 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5752</SortID>
    </Field>
    <Field Def="s16 hit15_DmyPoly2">
      <DisplayName>あたり15 ダミポリ2</DisplayName>
      <Description>カプセルのもうひとつの点の位置ダミポリ。-1だと球になる</Description>
      <Minimum>-1</Minimum>
      <Maximum>31999</Maximum>
      <SortID>5757</SortID>
    </Field>
    <Field Def="u8 hit4_hitType">
      <DisplayName>あたり4 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5704</SortID>
    </Field>
    <Field Def="u8 hit5_hitType">
      <DisplayName>あたり5 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5709</SortID>
    </Field>
    <Field Def="u8 hit6_hitType">
      <DisplayName>あたり6 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5714</SortID>
    </Field>
    <Field Def="u8 hit7_hitType">
      <DisplayName>あたり7 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5719</SortID>
    </Field>
    <Field Def="u8 hit8_hitType">
      <DisplayName>あたり8 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5724</SortID>
    </Field>
    <Field Def="u8 hit9_hitType">
      <DisplayName>あたり9 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5729</SortID>
    </Field>
    <Field Def="u8 hit10_hitType">
      <DisplayName>あたり10 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5734</SortID>
    </Field>
    <Field Def="u8 hit11_hitType">
      <DisplayName>あたり11 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5739</SortID>
    </Field>
    <Field Def="u8 hit12_hitType">
      <DisplayName>あたり12 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5744</SortID>
    </Field>
    <Field Def="u8 hit13_hitType">
      <DisplayName>あたり13 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5749</SortID>
    </Field>
    <Field Def="u8 hit14_hitType">
      <DisplayName>あたり14 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5754</SortID>
    </Field>
    <Field Def="u8 hit15_hitType">
      <DisplayName>あたり15 部位</DisplayName>
      <Enum>ATK_PARAM_HIT_TYPE</Enum>
      <Description>あたり部位</Description>
      <SortID>5759</SortID>
    </Field>
    <Field Def="u8 hti4_Priority">
      <DisplayName>あたり4 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5705</SortID>
    </Field>
    <Field Def="u8 hti5_Priority">
      <DisplayName>あたり5 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5710</SortID>
    </Field>
    <Field Def="u8 hti6_Priority">
      <DisplayName>あたり6 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5715</SortID>
    </Field>
    <Field Def="u8 hti7_Priority">
      <DisplayName>あたり7 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5720</SortID>
    </Field>
    <Field Def="u8 hti8_Priority">
      <DisplayName>あたり8 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5725</SortID>
    </Field>
    <Field Def="u8 hti9_Priority">
      <DisplayName>あたり9 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5730</SortID>
    </Field>
    <Field Def="u8 hti10_Priority">
      <DisplayName>あたり10 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5735</SortID>
    </Field>
    <Field Def="u8 hti11_Priority">
      <DisplayName>あたり11 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5740</SortID>
    </Field>
    <Field Def="u8 hti12_Priority">
      <DisplayName>あたり12 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5745</SortID>
    </Field>
    <Field Def="u8 hti13_Priority">
      <DisplayName>あたり13 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5750</SortID>
    </Field>
    <Field Def="u8 hti14_Priority">
      <DisplayName>あたり14 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5755</SortID>
    </Field>
    <Field Def="u8 hti15_Priority">
      <DisplayName>あたり15 優先順位</DisplayName>
      <Description>優先度。同時に2つ以上のあたりがあたった場合、優先度が高いほうを採用する。</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <SortID>5760</SortID>
    </Field>
    <Field Def="u16 defSfxMaterial1">
      <DisplayName>防御材質1[SFX]</DisplayName>
      <Enum>WEP_MATERIAL_DEF_SFX</Enum>
      <Description>ガード時のSFXに使用.1</Description>
      <Maximum>9999</Maximum>
      <SortID>1800</SortID>
    </Field>
    <Field Def="u16 defSeMaterial2">
      <DisplayName>防御材質2[SE]</DisplayName>
      <Enum>WEP_MATERIAL_DEF</Enum>
      <Description>ガード時のSEに使用2</Description>
      <Maximum>9999</Maximum>
      <SortID>2001</SortID>
    </Field>
    <Field Def="u16 defSfxMaterial2">
      <DisplayName>防御材質2[SFX]</DisplayName>
      <Enum>WEP_MATERIAL_DEF_SFX</Enum>
      <Description>ガード時のSFXに使用.2</Description>
      <Maximum>9999</Maximum>
      <SortID>1801</SortID>
    </Field>
    <Field Def="u16 atkDarkCorrection">
      <DisplayName>闇攻撃力補正値</DisplayName>
      <Description>PCのみ。闇攻撃力に掛ける倍率（弓の場合は、飛び道具を補正）</Description>
      <Maximum>60000</Maximum>
      <SortID>2520</SortID>
    </Field>
    <Field Def="u16 atkDark">
      <DisplayName>闇攻撃力</DisplayName>
      <Description>NPCのみ。闇攻撃の追加ダメージ</Description>
      <Maximum>9999</Maximum>
      <SortID>820</SortID>
    </Field>
    <Field Def="dummy8 pad5:1">
      <DisplayName>pad</DisplayName>
      <Description>pad</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>12003</SortID>
    </Field>
    <Field Def="u8 isDisableParry:1 = 1">
      <DisplayName>攻撃接触パリィ判定無効</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>新パリィ制御を無効化するかどうかのフラグです。攻撃側のダメージが、防御側でパリィ状態のキャラに接触した場合にパリィされたと判定する処理。</Description>
      <Maximum>1</Maximum>
      <SortID>1450</SortID>
    </Field>
    <Field Def="u8 isDisableBothHandsAtkBonus:1">
      <DisplayName>両手持ち時攻撃力ボーナス無効か</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>両手時の成長ステータス1.5倍適応を使わないようにする</Description>
      <Maximum>1</Maximum>
      <SortID>450</SortID>
    </Field>
    <Field Def="u8 isInvalidatedByNoDamageInAir:1">
      <DisplayName>限定無敵（空中のみ）で無効化されるか</DisplayName>
      <Enum>ATK_PARAM_BOOL</Enum>
      <Description>「無敵を貫通するか」が◯の場合、この設定は無視されます</Description>
      <Maximum>1</Maximum>
      <SortID>350</SortID>
    </Field>
    <Field Def="dummy8 pad2:4">
      <DisplayName>pad1</DisplayName>
      <SortID>12004</SortID>
    </Field>
    <Field Def="s8 dmgLevel_vsPlayer">
      <DisplayName>ダメージレベル 対プレイヤー</DisplayName>
      <Enum>ATKPARAM_REP_DMGTYPE</Enum>
      <Description>プレイヤーに対するダメージレベル。“0(デフォルト)”であれば使わない。“0(デフォルト)”以外の値域の意味は、《ダメージレベル》と同じ。</Description>
      <Minimum>0</Minimum>
      <Maximum>12</Maximum>
      <SortID>110</SortID>
    </Field>
    <Field Def="u16 statusAilmentAtkPowerCorrectRate = 100">
      <DisplayName>状態異常攻撃力倍率補正</DisplayName>
      <Description>特殊効果の状態異常攻撃力に対して、倍率補正を行う。</Description>
      <Maximum>60000</Maximum>
      <SortID>3120</SortID>
    </Field>
    <Field Def="u16 spEffectAtkPowerCorrectRate_byPoint = 100">
      <DisplayName>特殊効果攻撃力倍率補正（攻撃力ポイント）</DisplayName>
      <Description>特殊効果の～～攻撃力[point]に対して、倍率補正を行う。</Description>
      <Maximum>60000</Maximum>
      <SortID>3122</SortID>
    </Field>
    <Field Def="u16 spEffectAtkPowerCorrectRate_byRate = 100">
      <DisplayName>特殊効果攻撃力倍率補正（攻撃力倍率）</DisplayName>
      <Description>特殊効果の～～攻撃力倍率に対して、倍率補正を行う。</Description>
      <Maximum>60000</Maximum>
      <SortID>3123</SortID>
    </Field>
    <Field Def="u16 spEffectAtkPowerCorrectRate_byDmg = 100">
      <DisplayName>特殊効果攻撃力倍率補正（最終攻撃力倍率）</DisplayName>
      <Description>特殊効果の攻撃側：～～ダメージ倍率に対して、倍率補正を行う。</Description>
      <Maximum>60000</Maximum>
      <SortID>3124</SortID>
    </Field>
    <Field Def="u8 atkBehaviorId_2">
      <DisplayName>Behavior用識別値2</DisplayName>
      <Enum>ATKPARAM_BEHAVIOR_ID</Enum>
      <Description>Behavior用識別値：特定の時だけダメージモーションを再生する </Description>
      <SortID>9010</SortID>
    </Field>
    <Field Def="u8 throwDamageAttribute">
      <DisplayName>投げダメージ属性</DisplayName>
      <Enum>ATKPARAM_THROWATTR_TYPE</Enum>
      <Description>攻撃判定の投げダメージの属性。対応する特殊効果がかかるようになる。攻撃のATK_PATAM_THROWFLAG_TYPEが「2：投げ」の場合にのみ、機能を発揮する</Description>
      <Maximum>1</Maximum>
      <SortID>3625</SortID>
    </Field>
    <Field Def="u16 statusAilmentAtkPowerCorrectRate_byPoint = 100">
      <DisplayName>特殊効果状態異常補正（攻撃力ポイント）</DisplayName>
      <Description>特殊効果の「状態異常攻撃力倍率補正を適応するか」に対して、倍率補正を行う。</Description>
      <Maximum>60000</Maximum>
      <SortID>3121</SortID>
    </Field>
    <Field Def="s32 overwriteAttackElementCorrectId = -1">
      <DisplayName>攻撃属性補正ID上書き</DisplayName>
      <Description>攻撃属性を補正するパラメータのID上書き用</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="s16 decalBaseId1 = -1">
      <DisplayName>デカール識別子1</DisplayName>
      <Description>デカール識別子1(3桁)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>2120</SortID>
    </Field>
    <Field Def="s16 decalBaseId2 = -1">
      <DisplayName>デカール識別子2</DisplayName>
      <Description>デカール識別子2(3桁)</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>2130</SortID>
    </Field>
    <Field Def="u16 wepRegainHpScale = 100">
      <DisplayName>武器リゲイン量補正値</DisplayName>
      <Description>武器リゲイン量補正値</Description>
      <Maximum>60000</Maximum>
      <SortID>3150</SortID>
    </Field>
    <Field Def="u16 atkRegainHp">
      <DisplayName>攻撃リゲイン量</DisplayName>
      <Description>攻撃リゲイン量</Description>
      <Maximum>60000</Maximum>
      <SortID>3151</SortID>
    </Field>
    <Field Def="f32 regainableTimeScale = 1">
      <DisplayName>リゲイン可能時間補正倍率</DisplayName>
      <Description>リゲイン可能時間補正倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <SortID>3160</SortID>
    </Field>
    <Field Def="f32 regainableHpRateScale = 1">
      <DisplayName>リゲイン可能率補正倍率</DisplayName>
      <Description>リゲイン可能率補正倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>3170</SortID>
    </Field>
    <Field Def="s8 regainableSlotId = -1">
      <DisplayName>同一攻撃判定ID</DisplayName>
      <Description>同一攻撃判定ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>15</Maximum>
      <SortID>3180</SortID>
    </Field>
    <Field Def="u8 spAttributeVariationValue">
      <DisplayName>特殊属性バリエーション値</DisplayName>
      <Description>「特殊属性」と組み合わせて特殊属性によって発生するSFX、SEにバリエーションを持たせるための値(SEQ16473)</Description>
      <Maximum>99</Maximum>
      <SortID>1521</SortID>
    </Field>
    <Field Def="s16 parryForwardOffset">
      <DisplayName>パリィ成立条件の正面角度オフセット</DisplayName>
      <Description>パリィ成立条件の【崩される側】の正面角度オフセット</Description>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <SortID>1480</SortID>
    </Field>
    <Field Def="f32 atkSuperArmorCorrection">
      <DisplayName>SA攻撃力補正値</DisplayName>
      <Description>PCのみ。武器に設定された【基本値】にかける補正値</Description>
      <Minimum>0</Minimum>
      <Maximum>60000</Maximum>
      <Increment>0.1</Increment>
      <SortID>3110</SortID>
    </Field>
    <Field Def="u8 defSfxMaterialVariationValue">
      <DisplayName>防御材質バリエーション値</DisplayName>
      <Description>ガード時に使用される「防御材質1or2」と組み合わせてダメージSFX、SEのバリエーションを持たせるための値。(SEQ16473)</Description>
      <Maximum>99</Maximum>
      <SortID>1802</SortID>
    </Field>
    
    <Field Def="s16 unknown_1">
    </Field>
    
    <Field Def="dummy8 pad4[1]">
      <DisplayName>pad</DisplayName>
      <SortID>12005</SortID>
    </Field>
    
    <Field Def="s32 finalDamageRateId">
    </Field>
    
    <Field Def="s32 unknown_2" />
    <Field Def="f32 unknown_3" />
    <Field Def="f32 unknown_4" />
    <Field Def="u8 unknown_5a" />
    <Field Def="u8 unknown_5b" />
    <Field Def="u8 unknown_5c" />
    <Field Def="u8 unknown_5d" />
    
    <Field Def="dummy8 pad7_old[4]"/>
  </Fields>
</PARAMDEF>