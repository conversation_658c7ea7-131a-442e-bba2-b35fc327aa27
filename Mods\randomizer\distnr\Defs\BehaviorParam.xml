<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>BEHAVIOR_PARAM_ST</ParamType>
  <DataVersion>2</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="s32 variationId">
      <DisplayName>行動バリエーションID</DisplayName>
      <Description>攻撃パラメータ用のIDを算出する際に使用します。実機上では直接使用しません。</Description>
      <Minimum>0</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="s32 behaviorJudgeId">
      <DisplayName>行動判定ID</DisplayName>
      <Description>攻撃パラメータ用のIDを算出する際に使用します。このIDはTimeActEditorで入力される行動判定IDと一致させます。実機上では直接使用しません。</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="u8 ezStateBehaviorType_old">
      <DisplayName>IDルール用</DisplayName>
      <Description>ID算出ルール用</Description>
      <SortID>300</SortID>
    </Field>
    <Field Def="u8 refType">
      <DisplayName>参照IDタイプ</DisplayName>
      <Enum>BEHAVIOR_REF_TYPE</Enum>
      <Description>参照IDを間違わないように指定.</Description>
      <SortID>400</SortID>
    </Field>
    <Field Def="dummy8 pad2[2]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>1001</SortID>
    </Field>
    <Field Def="s32 refId = -1">
      <DisplayName>参照ID</DisplayName>
      <Description>攻撃力、飛び道具、特殊効果パラメータのID、refTypeによって使い分けられる。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="f32 consumeSA">
      <DisplayName>消費SA</DisplayName>
      <Description>行動時の消費SA量を設定.</Description>
      <Minimum>0</Minimum>
      <Maximum>9999.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>800</SortID>
    </Field>
    <Field Def="s32 stamina">
      <DisplayName>消費スタミナ</DisplayName>
      <Description>行動時の消費スタミナ量を設定.</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="s32 consumeDurability">
      <DisplayName>武器耐久度消費（飛び道具時のみ）</DisplayName>
      <Description>行動時の消費武器耐久度を設定.</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="u8 category">
      <DisplayName>カテゴリ</DisplayName>
      <Enum>BEHAVIOR_CATEGORY</Enum>
      <Description>スキルや、魔法、アイテムなどで、パラメータが変動する効果（エンチャントウェポンなど）があるので、│定した効果が、「武器攻撃のみをパワーアップする」といった効果に対応できるように行動ごとに設定するバリスタなど、設定の必要のないものは「なし」を設定する</Description>
      <SortID>600</SortID>
    </Field>
    <Field Def="u8 heroPoint">
      <DisplayName>消費人間性</DisplayName>
      <Description>行動時の消費人間性量を設定</Description>
      <SortID>1000</SortID>
    </Field>
    <Field Def="dummy8 pad1[2]">
      <DisplayName>pad</DisplayName>
      <SortID>1002</SortID>
    </Field>
  </Fields>
</PARAMDEF>