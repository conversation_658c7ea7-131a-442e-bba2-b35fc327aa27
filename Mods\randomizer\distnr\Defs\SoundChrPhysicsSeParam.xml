<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>SOUND_CHR_PHYSICS_SE_PARAM_ST</ParamType>
  <DataVersion>4</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>30</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>31</SortID>
    </Field>
    <Field Def="s32 ContactLandSeId = -1">
      <DisplayName>地面接触SEID</DisplayName>
      <Description>死亡後、地面と接触したときに発音するSEID。(-1：無効)。SEIDカテゴリーはc固定</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1</SortID>
    </Field>
    <Field Def="s32 ContactLandAddSeId = -1">
      <DisplayName>地面接触追加SEID(材質用)</DisplayName>
      <Description>死亡後、地面と接触したときに発音する追加SEID(材質用)。(-1：無効)。SEIDカテゴリーはc固定</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2</SortID>
    </Field>
    <Field Def="s32 ContactLandPlayNum = 1">
      <DisplayName>地面接触発音回数</DisplayName>
      <Description>死亡後、地面接触時に発音する回数</Description>
      <EditFlags>None</EditFlags>
      <Minimum>1</Minimum>
      <Maximum>16</Maximum>
      <SortID>3</SortID>
    </Field>
    <Field Def="u8 IsEnablePlayCountPerRigid">
      <DisplayName>地面接触発音回数を剛体単位でカウントするか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>死亡後地、面接触発音回数を剛体単位でカウントするか？(〇：剛体単位、×：キャラ単位)</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>4</SortID>
    </Field>
    <Field Def="dummy8 pad[3]">
      <SortID>32</SortID>
    </Field>
    <Field Def="f32 ContactLandMinImpuse = 20">
      <DisplayName>地面接触最小力積値</DisplayName>
      <Description>死亡後、地面接触判定に必要な最小力積値</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>1</Increment>
      <SortID>5</SortID>
    </Field>
    <Field Def="f32 ContactLandMinSpeed">
      <DisplayName>地面接触最小速度値</DisplayName>
      <Description>死亡後、地面接触判定に必要な最小速度値</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>1</Increment>
      <SortID>6</SortID>
    </Field>
    <Field Def="s32 ContactPlayerSeId = -1">
      <DisplayName>Player接触SEID</DisplayName>
      <Description>死亡後、Playerと接触したときに鳴らすSEID。(-1：無効)。SEIDカテゴリーはc固定</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>7</SortID>
    </Field>
    <Field Def="f32 ContactPlayerMinImpuse = 20">
      <DisplayName>Player接触最小力積値</DisplayName>
      <Description>死亡後、Player接触判定に必要な最小力積値</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>1</Increment>
      <SortID>8</SortID>
    </Field>
    <Field Def="f32 ContactPlayerMinSpeed">
      <DisplayName>Player接触最小速度値</DisplayName>
      <Description>死亡後、Player接触判定に必要な最小速度値</Description>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <Increment>1</Increment>
      <SortID>9</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx0 = -1">
      <DisplayName>接触判定剛体IDX0</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>10</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx1 = -1">
      <DisplayName>接触判定剛体IDX1</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>11</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx2 = -1">
      <DisplayName>接触判定剛体IDX2</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>12</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx3 = -1">
      <DisplayName>接触判定剛体IDX3</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>13</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx4 = -1">
      <DisplayName>接触判定剛体IDX4</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>14</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx5 = -1">
      <DisplayName>接触判定剛体IDX5</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>15</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx6 = -1">
      <DisplayName>接触判定剛体IDX6</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>16</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx7 = -1">
      <DisplayName>接触判定剛体IDX7</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>17</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx8 = -1">
      <DisplayName>接触判定剛体IDX8</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>18</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx9 = -1">
      <DisplayName>接触判定剛体IDX9</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>19</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx10 = -1">
      <DisplayName>接触判定剛体IDX10</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>20</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx11 = -1">
      <DisplayName>接触判定剛体IDX11</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>21</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx12 = -1">
      <DisplayName>接触判定剛体IDX12</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>22</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx13 = -1">
      <DisplayName>接触判定剛体IDX13</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>23</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx14 = -1">
      <DisplayName>接触判定剛体IDX14</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>24</SortID>
    </Field>
    <Field Def="s8 ContactCheckRigidIdx15 = -1">
      <DisplayName>接触判定剛体IDX15</DisplayName>
      <Description>接触判定する剛体のINDEXを指定します。SEを付けたい剛体だけ指定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>25</SortID>
    </Field>
  </Fields>
</PARAMDEF>