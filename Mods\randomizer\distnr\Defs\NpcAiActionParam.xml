<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>NPC_AI_ACTION_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 moveDir">
      <DisplayName>移動方向入力</DisplayName>
      <Enum>NPC_AI_ACTION_MOVE_DIR</Enum>
      <Description>入力する移動方向</Description>
      <SortID>100</SortID>
    </Field>
    <Field Def="u8 key1">
      <DisplayName>キー入力1</DisplayName>
      <Enum>NPC_AI_ACTION_KEY</Enum>
      <Description>入力するキー</Description>
      <SortID>200</SortID>
    </Field>
    <Field Def="u8 key2">
      <DisplayName>キー入力2</DisplayName>
      <Enum>NPC_AI_ACTION_KEY</Enum>
      <Description>入力するキー</Description>
      <SortID>300</SortID>
    </Field>
    <Field Def="u8 key3">
      <DisplayName>キー入力3</DisplayName>
      <Enum>NPC_AI_ACTION_KEY</Enum>
      <Description>入力するキー</Description>
      <SortID>400</SortID>
    </Field>
    <Field Def="u8 bMoveDirHold">
      <DisplayName>移動方向入力は長押し？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>入力する移動方向を長押し扱いするか</Description>
      <Maximum>1</Maximum>
      <SortID>110</SortID>
    </Field>
    <Field Def="u8 bKeyHold1">
      <DisplayName>キー入力1は長押し？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>入力するキーを長押扱いするか</Description>
      <Maximum>1</Maximum>
      <SortID>210</SortID>
    </Field>
    <Field Def="u8 bKeyHold2">
      <DisplayName>キー入力2は長押し？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>入力するキーを長押扱いするか</Description>
      <Maximum>1</Maximum>
      <SortID>310</SortID>
    </Field>
    <Field Def="u8 bKeyHold3">
      <DisplayName>キー入力3は長押し？</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>入力するキーを長押扱いするか</Description>
      <Maximum>1</Maximum>
      <SortID>410</SortID>
    </Field>
    <Field Def="s32 gestureId">
      <DisplayName>ジェスチャーID（どれかのキー入力がGESTUREの時のみ有効）</DisplayName>
      <Description>ジェスチャーID</Description>
      <Minimum>0</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="u8 bLifeEndSuccess">
      <DisplayName>寿命が尽きた時成功扱いにする</DisplayName>
      <Enum>ON_OFF</Enum>
      <Description>これがONならAIのゴールが寿命まで成功にならない</Description>
      <Maximum>1</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="dummy8 pad1[3]">
      <DisplayName>パッド</DisplayName>
      <Description>pad</Description>
      <SortID>601</SortID>
    </Field>
  </Fields>
</PARAMDEF>