#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序主类 - 负责应用程序的初始化和生命周期管理
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, QDir
from PySide6.QtGui import QIcon

from ..ui.main_window import MainWindow
from ..utils.logger import Logger
from ..utils.config import ConfigManager
from ..utils.theme import ThemeManager


class ModManagerApp:
    """ME3 Mod Manager 应用程序主类"""
    
    def __init__(self, argv):
        """初始化应用程序"""
        self.app = QApplication(argv)
        self.app.setApplicationName("ME3 Mod Manager")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("ME3 Mod Manager Team")
        
        # 初始化核心组件
        self._init_logger()
        self._init_config()
        self._init_theme()
        self._init_translator()
        self._init_ui()
        
    def _init_logger(self):
        """初始化日志系统"""
        self.logger = Logger()
        self.logger.info("应用程序启动")
        
    def _init_config(self):
        """初始化配置管理器"""
        self.config = ConfigManager()
        
    def _init_theme(self):
        """初始化主题管理器"""
        self.theme = ThemeManager()
        self.theme.apply_theme(self.config.get('theme', 'dark'))
        
    def _init_translator(self):
        """初始化国际化"""
        self.translator = QTranslator()
        locale = self.config.get('language', 'zh_CN')
        
        # 加载翻译文件
        translations_dir = Path(__file__).parent.parent / "resources" / "translations"
        if self.translator.load(f"app_{locale}", str(translations_dir)):
            self.app.installTranslator(self.translator)
            
    def _init_ui(self):
        """初始化用户界面"""
        self.main_window = MainWindow()
        
        # 设置应用程序图标
        icon_path = Path(__file__).parent.parent / "resources" / "icons" / "app.ico"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))
            
    def run(self):
        """运行应用程序"""
        try:
            self.main_window.show()
            return self.app.exec()
        except Exception as e:
            self.logger.error(f"应用程序运行错误: {e}")
            return 1
        finally:
            self.logger.info("应用程序退出")
