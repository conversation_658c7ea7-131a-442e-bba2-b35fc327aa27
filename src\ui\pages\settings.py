#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置页面 - 应用程序设置和偏好
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QGroupBox, QComboBox, QCheckBox,
                               QSpacerItem, QSizePolicy, QMessageBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ...utils.logger import Logger
from ...utils.config import ConfigManager
from ...utils.theme import ThemeManager


class SettingsPage(QWidget):
    """设置页面"""
    
    # 设置更新信号
    settings_updated = Signal()
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        self.config = ConfigManager()
        self.theme_manager = ThemeManager()
        
        self._init_ui()
        self._load_settings()
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("设置")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 外观设置组
        appearance_group = QGroupBox("外观设置")
        appearance_layout = QVBoxLayout(appearance_group)
        
        # 主题设置
        theme_layout = QHBoxLayout()
        theme_label = QLabel("主题:")
        theme_label.setFixedWidth(100)
        theme_layout.addWidget(theme_label)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["暗色主题", "亮色主题"])
        self.theme_combo.currentTextChanged.connect(self._on_theme_changed)
        theme_layout.addWidget(self.theme_combo)
        
        theme_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        appearance_layout.addLayout(theme_layout)
        
        # 语言设置
        language_layout = QHBoxLayout()
        language_label = QLabel("语言:")
        language_label.setFixedWidth(100)
        language_layout.addWidget(language_label)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        self.language_combo.currentTextChanged.connect(self._on_language_changed)
        language_layout.addWidget(self.language_combo)
        
        language_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        appearance_layout.addLayout(language_layout)
        
        layout.addWidget(appearance_group)
        
        # 下载设置组
        download_group = QGroupBox("下载设置")
        download_layout = QVBoxLayout(download_group)
        
        # 下载镜像设置
        mirror_layout = QHBoxLayout()
        mirror_label = QLabel("下载镜像:")
        mirror_label.setFixedWidth(100)
        mirror_layout.addWidget(mirror_label)
        
        self.mirror_combo = QComboBox()
        self.mirror_combo.addItems([
            "gh-proxy.com",
            "ghproxy.net", 
            "ghfast.top",
            "直连 (github.com)"
        ])
        self.mirror_combo.currentTextChanged.connect(self._on_mirror_changed)
        mirror_layout.addWidget(self.mirror_combo)
        
        mirror_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        download_layout.addLayout(mirror_layout)
        
        layout.addWidget(download_group)
        
        # 高级设置组
        advanced_group = QGroupBox("高级设置")
        advanced_layout = QVBoxLayout(advanced_group)
        
        # 启动时检查更新
        self.check_updates_checkbox = QCheckBox("启动时检查ME3更新")
        self.check_updates_checkbox.toggled.connect(self._on_check_updates_changed)
        advanced_layout.addWidget(self.check_updates_checkbox)
        
        # 自动应用破解
        self.auto_crack_checkbox = QCheckBox("配置游戏路径后自动应用破解")
        self.auto_crack_checkbox.toggled.connect(self._on_auto_crack_changed)
        advanced_layout.addWidget(self.auto_crack_checkbox)
        
        # 启用详细日志
        self.verbose_log_checkbox = QCheckBox("启用详细日志记录")
        self.verbose_log_checkbox.toggled.connect(self._on_verbose_log_changed)
        advanced_layout.addWidget(self.verbose_log_checkbox)
        
        layout.addWidget(advanced_group)
        
        # 操作按钮组
        actions_group = QGroupBox("操作")
        actions_layout = QVBoxLayout(actions_group)
        
        # 按钮布局
        buttons_layout = QHBoxLayout()
        
        # 重置设置按钮
        self.reset_btn = QPushButton("🔄 重置所有设置")
        self.reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        self.reset_btn.clicked.connect(self._reset_settings)
        buttons_layout.addWidget(self.reset_btn)
        
        # 清理缓存按钮
        self.clear_cache_btn = QPushButton("🗑️ 清理缓存")
        self.clear_cache_btn.clicked.connect(self._clear_cache)
        buttons_layout.addWidget(self.clear_cache_btn)
        
        # 打开日志文件夹按钮
        self.open_logs_btn = QPushButton("📁 打开日志文件夹")
        self.open_logs_btn.clicked.connect(self._open_logs_folder)
        buttons_layout.addWidget(self.open_logs_btn)
        
        buttons_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        actions_layout.addLayout(buttons_layout)
        
        layout.addWidget(actions_group)
        
        # 关于信息组
        about_group = QGroupBox("关于")
        about_layout = QVBoxLayout(about_group)
        
        about_text = QLabel("""
        <b>ME3 Mod Manager v1.0.0</b><br>
        现代化的ME3 Mod管理工具<br><br>
        
        <b>功能特性:</b><br>
        • 自动下载和管理ME3工具<br>
        • 游戏路径配置和破解管理<br>
        • 可视化Mod配置<br>
        • 现代化UI设计，支持主题切换<br><br>
        
        <b>技术栈:</b><br>
        • PySide6 (Qt6)<br>
        • Python 3.8+<br>
        • 模块化架构设计
        """)
        about_text.setStyleSheet("color: #cccccc; line-height: 1.4;")
        about_text.setWordWrap(True)
        about_layout.addWidget(about_text)
        
        layout.addWidget(about_group)
        
        # 底部弹性空间
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
    def _load_settings(self):
        """加载设置"""
        # 加载主题设置
        current_theme = self.config.get('theme', 'dark')
        if current_theme == 'dark':
            self.theme_combo.setCurrentText("暗色主题")
        else:
            self.theme_combo.setCurrentText("亮色主题")
            
        # 加载语言设置
        current_language = self.config.get('language', 'zh_CN')
        if current_language == 'zh_CN':
            self.language_combo.setCurrentText("简体中文")
        else:
            self.language_combo.setCurrentText("English")
            
        # 加载下载镜像设置
        current_mirror = self.config.get('me3.download_mirror', 'gh-proxy.com')
        self.mirror_combo.setCurrentText(current_mirror)
        
        # 加载高级设置
        self.check_updates_checkbox.setChecked(self.config.get('advanced.check_updates', True))
        self.auto_crack_checkbox.setChecked(self.config.get('advanced.auto_crack', False))
        self.verbose_log_checkbox.setChecked(self.config.get('advanced.verbose_log', False))
        
    def _on_theme_changed(self, theme_text: str):
        """主题改变处理"""
        theme_name = 'dark' if theme_text == "暗色主题" else 'light'
        self.config.set('theme', theme_name)
        self.theme_manager.apply_theme(theme_name)
        self.logger.info(f"主题已切换到: {theme_name}")
        
    def _on_language_changed(self, language_text: str):
        """语言改变处理"""
        language_code = 'zh_CN' if language_text == "简体中文" else 'en_US'
        self.config.set('language', language_code)
        self.logger.info(f"语言已切换到: {language_code}")
        
        # 提示需要重启应用
        QMessageBox.information(self, "提示", "语言设置将在下次启动时生效")
        
    def _on_mirror_changed(self, mirror_text: str):
        """下载镜像改变处理"""
        self.config.set('me3.download_mirror', mirror_text)
        self.logger.info(f"下载镜像已设置为: {mirror_text}")
        
    def _on_check_updates_changed(self, checked: bool):
        """检查更新设置改变"""
        self.config.set('advanced.check_updates', checked)
        
    def _on_auto_crack_changed(self, checked: bool):
        """自动破解设置改变"""
        self.config.set('advanced.auto_crack', checked)
        
    def _on_verbose_log_changed(self, checked: bool):
        """详细日志设置改变"""
        self.config.set('advanced.verbose_log', checked)
        
    def _reset_settings(self):
        """重置所有设置"""
        reply = QMessageBox.question(
            self, 
            "确认重置", 
            "确定要重置所有设置到默认值吗？\n此操作不可撤销。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 删除配置文件
                config_file = self.config.config_file
                if config_file.exists():
                    config_file.unlink()
                    
                # 重新初始化配置
                self.config = ConfigManager()
                self._load_settings()
                
                # 应用默认主题
                self.theme_manager.apply_theme('dark')
                
                self.logger.info("所有设置已重置到默认值")
                QMessageBox.information(self, "成功", "所有设置已重置到默认值")
                
            except Exception as e:
                self.logger.error(f"重置设置失败: {e}")
                QMessageBox.critical(self, "错误", f"重置设置失败: {e}")
                
    def _clear_cache(self):
        """清理缓存"""
        try:
            import shutil
            from pathlib import Path
            
            # 清理可能的缓存目录
            cache_dirs = [
                Path("cache"),
                Path("temp"),
                Path("downloads")
            ]
            
            cleared_count = 0
            for cache_dir in cache_dirs:
                if cache_dir.exists():
                    shutil.rmtree(cache_dir)
                    cleared_count += 1
                    
            self.logger.info(f"已清理 {cleared_count} 个缓存目录")
            QMessageBox.information(self, "成功", f"已清理 {cleared_count} 个缓存目录")
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {e}")
            QMessageBox.critical(self, "错误", f"清理缓存失败: {e}")
            
    def _open_logs_folder(self):
        """打开日志文件夹"""
        try:
            import os
            import sys
            import subprocess
            from pathlib import Path

            logs_dir = Path("logs")
            if not logs_dir.exists():
                logs_dir.mkdir()

            # 在文件管理器中打开日志文件夹
            if os.name == 'nt':  # Windows
                os.startfile(logs_dir)
            elif os.name == 'posix':  # macOS and Linux
                subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', logs_dir])

        except Exception as e:
            self.logger.error(f"打开日志文件夹失败: {e}")
            QMessageBox.critical(self, "错误", f"打开日志文件夹失败: {e}")
