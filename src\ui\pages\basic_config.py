#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础配置页面 - 游戏路径配置和破解功能
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QGroupBox, QLineEdit, QFileDialog,
                               QMessageBox, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
from pathlib import Path

from ...utils.logger import Logger
from ...utils.config import ConfigManager
from ...business.game_manager import GameManager


class BasicConfigPage(QWidget):
    """基础配置页面"""
    
    # 配置更新信号
    config_updated = Signal()
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        self.config = ConfigManager()
        self.game_manager = GameManager()

        self._init_ui()
        self._connect_signals()
        self._load_config()
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("基础配置")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 游戏路径配置组
        game_group = QGroupBox("游戏路径配置")
        game_layout = QVBoxLayout(game_group)
        
        # 说明文本
        desc_label = QLabel("请选择 nightreign.exe 游戏文件的路径：")
        desc_label.setStyleSheet("color: #cccccc; margin-bottom: 10px;")
        game_layout.addWidget(desc_label)
        
        # 路径输入区域
        path_layout = QHBoxLayout()
        
        self.game_path_edit = QLineEdit()
        self.game_path_edit.setPlaceholderText("例如: H:/SteamLibrary/steamapps/common/ELDEN RING NIGHTREIGN/Game/nightreign.exe")
        self.game_path_edit.setReadOnly(True)
        path_layout.addWidget(self.game_path_edit)
        
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.setFixedWidth(100)
        self.browse_btn.clicked.connect(self._browse_game_path)
        path_layout.addWidget(self.browse_btn)
        
        game_layout.addLayout(path_layout)
        
        # 路径验证状态
        self.path_status_label = QLabel()
        self.path_status_label.setStyleSheet("margin-top: 5px;")
        game_layout.addWidget(self.path_status_label)
        
        layout.addWidget(game_group)
        
        # 破解功能组
        crack_group = QGroupBox("破解功能")
        crack_layout = QVBoxLayout(crack_group)
        
        # 破解说明
        crack_desc = QLabel("破解功能会将 OnlineFix 文件夹中的文件复制到游戏根目录。")
        crack_desc.setStyleSheet("color: #cccccc; margin-bottom: 15px;")
        crack_layout.addWidget(crack_desc)
        
        # 破解按钮区域
        crack_buttons_layout = QHBoxLayout()
        
        self.crack_btn = QPushButton("🔓 应用破解")
        self.crack_btn.setFixedHeight(40)
        self.crack_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #404040;
                color: #808080;
            }
        """)
        self.crack_btn.clicked.connect(self._apply_crack)
        self.crack_btn.setEnabled(False)
        crack_buttons_layout.addWidget(self.crack_btn)
        
        self.remove_crack_btn = QPushButton("🔒 移除破解")
        self.remove_crack_btn.setFixedHeight(40)
        self.remove_crack_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:disabled {
                background-color: #404040;
                color: #808080;
            }
        """)
        self.remove_crack_btn.clicked.connect(self._remove_crack)
        self.remove_crack_btn.setEnabled(False)
        crack_buttons_layout.addWidget(self.remove_crack_btn)
        
        crack_layout.addLayout(crack_buttons_layout)
        
        # 破解状态
        self.crack_status_label = QLabel()
        self.crack_status_label.setStyleSheet("margin-top: 10px; font-weight: bold;")
        crack_layout.addWidget(self.crack_status_label)
        
        layout.addWidget(crack_group)
        
        # 底部弹性空间
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

    def _connect_signals(self):
        """连接信号"""
        # 游戏管理器信号
        self.game_manager.game_path_changed.connect(self._on_game_path_changed)
        self.game_manager.crack_status_changed.connect(self._on_crack_status_changed)
        
    def _load_config(self):
        """加载配置"""
        # 加载游戏路径
        game_path = self.game_manager.get_game_path()
        if game_path:
            self.game_path_edit.setText(game_path)
            self._validate_game_path(game_path)

        # 同步并更新破解状态
        self.game_manager.sync_crack_status()
        self._update_crack_status()
        
    def _browse_game_path(self):
        """浏览游戏路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择 nightreign.exe 文件",
            "",
            "可执行文件 (*.exe);;所有文件 (*.*)"
        )
        
        if file_path:
            self.game_path_edit.setText(file_path)
            if self._validate_game_path(file_path):
                self._save_game_path(file_path)
                
    def _validate_game_path(self, path: str) -> bool:
        """验证游戏路径"""
        if not path:
            self.path_status_label.setText("")
            self._update_buttons_state(False)
            return False

        is_valid = self.game_manager.validate_game_path(path)

        if not is_valid:
            path_obj = Path(path)
            if not path_obj.exists():
                self.path_status_label.setText("❌ 文件不存在")
            elif path_obj.name.lower() != "nightreign.exe":
                self.path_status_label.setText("❌ 请选择 nightreign.exe 文件")
            else:
                self.path_status_label.setText("❌ 游戏路径无效")

            self.path_status_label.setStyleSheet("color: #e74c3c; margin-top: 5px;")
            self._update_buttons_state(False)
            return False

        self.path_status_label.setText("✅ 游戏路径有效")
        self.path_status_label.setStyleSheet("color: #27ae60; margin-top: 5px;")
        self._update_buttons_state(True)
        return True
        
    def _save_game_path(self, path: str):
        """保存游戏路径到配置文件"""
        try:
            success = self.game_manager.set_game_path(path)
            if success:
                self.logger.info(f"游戏路径已保存: {path}")
                self.config_updated.emit()
            else:
                raise Exception("游戏管理器保存路径失败")

        except Exception as e:
            self.logger.error(f"保存游戏路径失败: {e}")
            QMessageBox.critical(self, "错误", f"保存游戏路径失败: {e}")
            
    def _update_buttons_state(self, game_path_valid: bool):
        """更新按钮状态"""
        self.crack_btn.setEnabled(game_path_valid)
        self.remove_crack_btn.setEnabled(game_path_valid)
        
    def _apply_crack(self):
        """应用破解"""
        try:
            success = self.game_manager.apply_crack()
            if success:
                QMessageBox.information(self, "成功", "破解已成功应用")
                self.config_updated.emit()
            else:
                QMessageBox.critical(self, "错误", "应用破解失败，请查看日志获取详细信息")

        except Exception as e:
            self.logger.error(f"应用破解失败: {e}")
            QMessageBox.critical(self, "错误", f"应用破解失败: {e}")
            
    def _remove_crack(self):
        """移除破解"""
        try:
            success = self.game_manager.remove_crack()
            if success:
                QMessageBox.information(self, "成功", "破解已成功移除")
                self.config_updated.emit()
            else:
                QMessageBox.critical(self, "错误", "移除破解失败，请查看日志获取详细信息")

        except Exception as e:
            self.logger.error(f"移除破解失败: {e}")
            QMessageBox.critical(self, "错误", f"移除破解失败: {e}")
            
    def _update_crack_status(self):
        """更新破解状态显示"""
        is_cracked = self.game_manager.is_cracked()

        if is_cracked:
            self.crack_status_label.setText("🔓 当前状态: 已破解")
            self.crack_status_label.setStyleSheet("color: #27ae60; margin-top: 10px; font-weight: bold;")
        else:
            self.crack_status_label.setText("🔒 当前状态: 未破解")
            self.crack_status_label.setStyleSheet("color: #e74c3c; margin-top: 10px; font-weight: bold;")

    def _on_game_path_changed(self, path: str):
        """游戏路径改变处理"""
        self.game_path_edit.setText(path)
        self._validate_game_path(path)

    def _on_crack_status_changed(self, is_cracked: bool):
        """破解状态改变处理"""
        self._update_crack_status()
