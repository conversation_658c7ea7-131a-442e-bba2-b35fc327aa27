#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主题管理器 - 应用程序主题和样式管理
"""

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QObject, Signal
from pathlib import Path


class ThemeManager(QObject):
    """主题管理器"""
    
    theme_changed = Signal(str)  # 主题变化信号
    
    def __init__(self):
        super().__init__()
        self.current_theme = "dark"
        self.themes = {
            "dark": self._get_dark_theme(),
            "light": self._get_light_theme()
        }
    
    def _get_dark_theme(self) -> str:
        """获取暗色主题样式"""
        return """
        /* 暗色主题 */
        QMainWindow {
            background-color: #1e1e1e;
            color: #ffffff;
        }
        
        /* 自定义标题栏 */
        #titleBar {
            background-color: #2d2d2d;
            border-bottom: 1px solid #3e3e3e;
            min-height: 40px;
            max-height: 40px;
        }
        
        #titleLabel {
            color: #ffffff;
            font-size: 14px;
            font-weight: bold;
            padding-left: 10px;
        }
        
        #titleButtons {
            background-color: transparent;
        }
        
        #minimizeBtn, #maximizeBtn, #closeBtn {
            background-color: transparent;
            border: none;
            color: #ffffff;
            font-size: 16px;
            min-width: 45px;
            max-width: 45px;
            min-height: 40px;
            max-height: 40px;
        }
        
        #minimizeBtn:hover, #maximizeBtn:hover {
            background-color: #404040;
        }
        
        #closeBtn:hover {
            background-color: #e81123;
        }
        
        /* 侧边栏 */
        #sideBar {
            background-color: #252526;
            border-right: 1px solid #3e3e3e;
            min-width: 250px;
            max-width: 250px;
        }
        
        /* 侧边栏按钮 */
        .sideBarButton {
            background-color: transparent;
            border: none;
            color: #cccccc;
            text-align: left;
            padding: 15px 20px;
            font-size: 14px;
            border-radius: 0px;
        }
        
        .sideBarButton:hover {
            background-color: #2a2d2e;
            color: #ffffff;
        }
        
        .sideBarButton:checked {
            background-color: #094771;
            color: #ffffff;
            border-left: 3px solid #0078d4;
        }
        
        /* 主内容区域 */
        #contentArea {
            background-color: #1e1e1e;
            border: none;
        }
        
        /* 按钮样式 */
        QPushButton {
            background-color: #0078d4;
            color: #ffffff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #404040;
            color: #808080;
        }
        
        /* 输入框样式 */
        QLineEdit {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #5a5a5a;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        QLineEdit:focus {
            border: 1px solid #0078d4;
        }
        
        /* 文本区域 */
        QTextEdit, QPlainTextEdit {
            background-color: #1e1e1e;
            color: #ffffff;
            border: 1px solid #3e3e3e;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        
        /* 滚动条 */
        QScrollBar:vertical {
            background-color: #2d2d2d;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #5a5a5a;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #6a6a6a;
        }
        
        /* 复选框 */
        QCheckBox {
            color: #ffffff;
            font-size: 14px;
        }
        
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #5a5a5a;
            border-radius: 3px;
            background-color: #3c3c3c;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }
        
        /* 标签 */
        QLabel {
            color: #ffffff;
            font-size: 14px;
        }
        
        /* 分组框 */
        QGroupBox {
            color: #ffffff;
            font-size: 14px;
            font-weight: bold;
            border: 1px solid #3e3e3e;
            border-radius: 4px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        """
    
    def _get_light_theme(self) -> str:
        """获取亮色主题样式"""
        return """
        /* 亮色主题 */
        QMainWindow {
            background-color: #ffffff;
            color: #000000;
        }
        
        /* 自定义标题栏 */
        #titleBar {
            background-color: #f0f0f0;
            border-bottom: 1px solid #d0d0d0;
            min-height: 40px;
            max-height: 40px;
        }
        
        #titleLabel {
            color: #000000;
            font-size: 14px;
            font-weight: bold;
            padding-left: 10px;
        }
        
        #titleButtons {
            background-color: transparent;
        }
        
        #minimizeBtn, #maximizeBtn, #closeBtn {
            background-color: transparent;
            border: none;
            color: #000000;
            font-size: 16px;
            min-width: 45px;
            max-width: 45px;
            min-height: 40px;
            max-height: 40px;
        }
        
        #minimizeBtn:hover, #maximizeBtn:hover {
            background-color: #e0e0e0;
        }
        
        #closeBtn:hover {
            background-color: #e81123;
            color: #ffffff;
        }
        
        /* 侧边栏 */
        #sideBar {
            background-color: #f8f8f8;
            border-right: 1px solid #d0d0d0;
            min-width: 250px;
            max-width: 250px;
        }
        
        /* 侧边栏按钮 */
        .sideBarButton {
            background-color: transparent;
            border: none;
            color: #333333;
            text-align: left;
            padding: 15px 20px;
            font-size: 14px;
            border-radius: 0px;
        }
        
        .sideBarButton:hover {
            background-color: #e8e8e8;
            color: #000000;
        }
        
        .sideBarButton:checked {
            background-color: #cce7ff;
            color: #000000;
            border-left: 3px solid #0078d4;
        }
        
        /* 主内容区域 */
        #contentArea {
            background-color: #ffffff;
            border: none;
        }
        
        /* 按钮样式 */
        QPushButton {
            background-color: #0078d4;
            color: #ffffff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        
        /* 输入框样式 */
        QLineEdit {
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #cccccc;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
        }
        
        QLineEdit:focus {
            border: 1px solid #0078d4;
        }
        
        /* 文本区域 */
        QTextEdit, QPlainTextEdit {
            background-color: #ffffff;
            color: #000000;
            border: 1px solid #cccccc;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        
        /* 滚动条 */
        QScrollBar:vertical {
            background-color: #f0f0f0;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: #cccccc;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #aaaaaa;
        }
        
        /* 复选框 */
        QCheckBox {
            color: #000000;
            font-size: 14px;
        }
        
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #cccccc;
            border-radius: 3px;
            background-color: #ffffff;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border-color: #0078d4;
        }
        
        /* 标签 */
        QLabel {
            color: #000000;
            font-size: 14px;
        }
        
        /* 分组框 */
        QGroupBox {
            color: #000000;
            font-size: 14px;
            font-weight: bold;
            border: 1px solid #cccccc;
            border-radius: 4px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        """
    
    def apply_theme(self, theme_name: str):
        """应用主题"""
        if theme_name in self.themes:
            self.current_theme = theme_name
            app = QApplication.instance()
            if app:
                app.setStyleSheet(self.themes[theme_name])
                self.theme_changed.emit(theme_name)
    
    def get_current_theme(self) -> str:
        """获取当前主题名称"""
        return self.current_theme
    
    def get_available_themes(self) -> list:
        """获取可用主题列表"""
        return list(self.themes.keys())
