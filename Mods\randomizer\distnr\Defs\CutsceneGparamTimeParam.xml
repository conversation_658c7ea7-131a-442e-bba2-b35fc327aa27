<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>CUTSCENE_GPARAM_TIME_PARAM_ST</ParamType>
  <DataVersion>3</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="u8 disableParam_Debug:1">
      <DisplayName>デバッグパラメータか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータは全パッケージから除外します（デバッグ用なので）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>0</Maximum>
      <SortID>1</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve1:6">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>9999</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>10000</SortID>
    </Field>
    <Field Def="u8 DstTimezone_Morning">
      <DisplayName>朝</DisplayName>
      <Enum>CUTSCENE_TIMEZONE_TYPE</Enum>
      <Description>朝(変換時刻はカットシーン時間変換設定シートを参照）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="u8 DstTimezone_Noon">
      <DisplayName>昼A</DisplayName>
      <Enum>CUTSCENE_TIMEZONE_TYPE</Enum>
      <Description>昼A(変換時刻はカットシーン時間変換設定シートを参照）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>101</SortID>
    </Field>
    <Field Def="u8 DstTimezone_AfterNoon">
      <DisplayName>昼B</DisplayName>
      <Enum>CUTSCENE_TIMEZONE_TYPE</Enum>
      <Description>昼B(変換時刻はカットシーン時間変換設定シートを参照）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>102</SortID>
    </Field>
    <Field Def="u8 DstTimezone_Evening">
      <DisplayName>夕</DisplayName>
      <Enum>CUTSCENE_TIMEZONE_TYPE</Enum>
      <Description>夕(変換時刻はカットシーン時間変換設定シートを参照）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>103</SortID>
    </Field>
    <Field Def="u8 DstTimezone_Night">
      <DisplayName>夜</DisplayName>
      <Enum>CUTSCENE_TIMEZONE_TYPE</Enum>
      <Description>夜(変換時刻はカットシーン時間変換設定シートを参照）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>104</SortID>
    </Field>
    <Field Def="u8 DstTimezone_DeepNightA">
      <DisplayName>深夜A</DisplayName>
      <Enum>CUTSCENE_TIMEZONE_TYPE</Enum>
      <Description>深夜A(変換時刻はカットシーン時間変換設定シートを参照）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>105</SortID>
    </Field>
    <Field Def="u8 DstTimezone_DeepNightB">
      <DisplayName>深夜B</DisplayName>
      <Enum>CUTSCENE_TIMEZONE_TYPE</Enum>
      <Description>深夜B(変換時刻はカットシーン時間変換設定シートを参照）</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>106</SortID>
    </Field>
    <Field Def="dummy8 reserved[1]">
      <Description>reserved</Description>
      <SortID>9999</SortID>
    </Field>
    <Field Def="f32 PostPlayIngameTime = -1">
      <DisplayName>再生終了時のインゲーム時刻指定[hour]</DisplayName>
      <Description>再生終了時のインゲーム時刻指定[hour][-1.0～24.0](-1(0より小さい)：何もしない)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>24</Maximum>
      <Increment>0.1</Increment>
      <SortID>9</SortID>
    </Field>
  </Fields>
</PARAMDEF>