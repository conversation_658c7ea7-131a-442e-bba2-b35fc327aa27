#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器 - 应用程序配置管理
"""

import json
import os
from pathlib import Path
from typing import Any, Dict


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        """初始化配置管理器"""
        self.config_dir = Path("config")
        self.config_dir.mkdir(exist_ok=True)
        self.config_file = self.config_dir / "app_config.json"
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        default_config = {
            "theme": "dark",
            "language": "zh_CN",
            "window": {
                "width": 1200,
                "height": 800,
                "maximized": False
            },
            "game": {
                "path": "",
                "cracked": False
            },
            "me3": {
                "version": "",
                "download_mirror": "gh-proxy.com"
            },
            "mods": {
                "enabled": []
            }
        }
        
        if not self.config_file.exists():
            self._save_config(default_config)
            return default_config
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置，确保所有必要的键都存在
                return self._merge_config(default_config, config)
        except (json.JSONDecodeError, FileNotFoundError):
            return default_config
    
    def _merge_config(self, default: Dict, user: Dict) -> Dict:
        """合并配置，确保所有默认键都存在"""
        result = default.copy()
        for key, value in user.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._merge_config(result[key], value)
            else:
                result[key] = value
        return result
    
    def _save_config(self, config: Dict[str, Any] = None):
        """保存配置文件"""
        config_to_save = config or self._config
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        config = self._config
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
        self._save_config()
    
    def save(self):
        """手动保存配置"""
        self._save_config()
