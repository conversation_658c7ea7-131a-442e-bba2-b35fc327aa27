#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序测试脚本
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.core.app import ModManagerApp


def test_app():
    """测试应用程序启动"""
    print("🚀 启动ME3 Mod Manager测试...")
    
    try:
        app = ModManagerApp()
        print("✅ 应用程序初始化成功")
        
        # 显示主窗口
        app.main_window.show()
        print("✅ 主窗口显示成功")
        
        # 检查各个组件
        print("\n📋 组件检查:")
        print(f"  - 主窗口: {'✅' if app.main_window else '❌'}")
        print(f"  - 日志系统: {'✅' if app.logger else '❌'}")
        print(f"  - 配置管理: {'✅' if app.config else '❌'}")
        print(f"  - 主题管理: {'✅' if app.theme_manager else '❌'}")
        
        # 检查页面
        main_window = app.main_window
        print(f"  - 仪表板页面: {'✅' if hasattr(main_window, 'dashboard_page') else '❌'}")
        print(f"  - 基础配置页面: {'✅' if hasattr(main_window, 'basic_config_page') else '❌'}")
        print(f"  - Mod配置页面: {'✅' if hasattr(main_window, 'mod_config_page') else '❌'}")
        print(f"  - 设置页面: {'✅' if hasattr(main_window, 'settings_page') else '❌'}")
        
        print("\n🎉 所有组件检查完成！")
        print("💡 提示: 应用程序已启动，您可以在GUI中测试各项功能")
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(test_app())
