#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载对话框 - ME3下载进度显示
"""

from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QProgressBar, QTextEdit, QGroupBox)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from ...business.me3_manager import ME3Manager
from ...utils.logger import Logger


class DownloadDialog(QDialog):
    """下载对话框"""
    
    download_finished = Signal(bool)  # 下载完成信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = Logger()
        self.me3_manager = ME3Manager()
        self.version_info = None
        
        self._init_ui()
        self._connect_signals()
        
    def _init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("下载ME3")
        self.setFixedSize(500, 400)
        self.setModal(True)
        
        # 设置窗口标志
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("下载ME3工具")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)
        
        # 版本信息组
        version_group = QGroupBox("版本信息")
        version_layout = QVBoxLayout(version_group)
        
        self.version_label = QLabel("检查版本中...")
        version_layout.addWidget(self.version_label)
        
        self.size_label = QLabel("")
        version_layout.addWidget(self.size_label)
        
        layout.addWidget(version_group)
        
        # 下载进度组
        progress_group = QGroupBox("下载进度")
        progress_layout = QVBoxLayout(progress_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        progress_layout.addWidget(self.progress_bar)
        
        # 状态标签
        self.status_label = QLabel("准备下载...")
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(progress_group)
        
        # 日志区域
        log_group = QGroupBox("下载日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(120)
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                background-color: #1e1e1e;
                color: #ffffff;
            }
        """)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 按钮区域
        buttons_layout = QHBoxLayout()
        
        self.download_btn = QPushButton("开始下载")
        self.download_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                font-weight: bold;
                padding: 8px 20px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #404040;
                color: #808080;
            }
        """)
        self.download_btn.clicked.connect(self._start_download)
        buttons_layout.addWidget(self.download_btn)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_btn)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.setVisible(False)
        self.close_btn.clicked.connect(self.accept)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addLayout(buttons_layout)
        
    def _connect_signals(self):
        """连接信号"""
        self.me3_manager.download_progress.connect(self._on_progress_updated)
        self.me3_manager.download_status.connect(self._on_status_updated)
        self.me3_manager.download_completed.connect(self._on_download_completed)
        
    def show_download_dialog(self):
        """显示下载对话框"""
        # 检查最新版本
        self._check_version()
        self.show()
        
    def _check_version(self):
        """检查版本信息"""
        try:
            self._add_log("检查ME3最新版本...")
            
            self.version_info = self.me3_manager.check_latest_version()
            
            if self.version_info:
                version_text = f"最新版本: {self.version_info['version']}"
                self.version_label.setText(version_text)
                
                # 显示文件大小
                size_mb = self.version_info['size'] / (1024 * 1024)
                size_text = f"文件大小: {size_mb:.1f} MB"
                self.size_label.setText(size_text)
                
                self._add_log(f"发现最新版本: {self.version_info['version']}")
                self._add_log(f"文件大小: {size_mb:.1f} MB")
                
                # 检查当前版本
                current_version = self.me3_manager.get_current_version()
                if current_version != "未安装":
                    self._add_log(f"当前版本: {current_version}")
                    
                self.download_btn.setEnabled(True)
                
            else:
                self.version_label.setText("无法获取版本信息")
                self.size_label.setText("请检查网络连接")
                self._add_log("获取版本信息失败，请检查网络连接")
                self.download_btn.setEnabled(False)
                
        except Exception as e:
            self.logger.error(f"检查版本失败: {e}")
            self.version_label.setText("版本检查失败")
            self._add_log(f"版本检查失败: {e}")
            self.download_btn.setEnabled(False)
            
    def _start_download(self):
        """开始下载"""
        if not self.version_info:
            self._add_log("错误: 没有版本信息")
            return
            
        self.download_btn.setEnabled(False)
        self.cancel_btn.setText("取消下载")
        
        self._add_log("开始下载ME3...")
        
        # 启动下载
        success = self.me3_manager.download_me3(self.version_info)
        if not success:
            self._add_log("启动下载失败")
            self.download_btn.setEnabled(True)
            self.cancel_btn.setText("取消")
            
    def _on_progress_updated(self, progress: int):
        """更新进度"""
        self.progress_bar.setValue(progress)
        
    def _on_status_updated(self, status: str):
        """更新状态"""
        self.status_label.setText(status)
        self._add_log(status)
        
    def _on_download_completed(self, success: bool, message: str):
        """下载完成"""
        if success:
            self.progress_bar.setValue(100)
            self.status_label.setText("下载完成")
            self._add_log("✅ " + message)
            
            # 更新按钮状态
            self.download_btn.setVisible(False)
            self.cancel_btn.setVisible(False)
            self.close_btn.setVisible(True)
            
        else:
            self.status_label.setText("下载失败")
            self._add_log("❌ " + message)
            
            # 重新启用下载按钮
            self.download_btn.setEnabled(True)
            self.cancel_btn.setText("取消")
            
        self.download_finished.emit(success)
        
    def _add_log(self, message: str):
        """添加日志消息"""
        from PySide6.QtCore import QDateTime
        timestamp = QDateTime.currentDateTime().toString('hh:mm:ss')
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def closeEvent(self, event):
        """关闭事件"""
        # 如果正在下载，询问是否确认关闭
        if (self.me3_manager.download_worker and 
            self.me3_manager.download_worker.isRunning()):
            
            from PySide6.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self,
                "确认关闭",
                "下载正在进行中，确定要关闭吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                event.ignore()
                return
                
            # 停止下载线程
            self.me3_manager.download_worker.terminate()
            self.me3_manager.download_worker.wait()
            
        event.accept()
