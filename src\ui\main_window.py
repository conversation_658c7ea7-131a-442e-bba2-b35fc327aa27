#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口 - 应用程序主界面
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                               QStackedWidget, QFrame)
from PySide6.QtCore import Qt, QPoint, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QMouseEvent

from .components.title_bar import CustomTitleBar
from .components.side_bar import SideBar
from .pages.dashboard import DashboardPage
from .pages.basic_config import BasicConfigPage
from .pages.mod_config import ModConfigPage
from .pages.settings import SettingsPage
from ..utils.logger import Logger


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        self.drag_position = QPoint()
        
        self._init_ui()
        self._setup_window()
        
    def _init_ui(self):
        """初始化用户界面"""
        # 设置无边框窗口
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建标题栏
        self.title_bar = CustomTitleBar(self)
        main_layout.addWidget(self.title_bar)
        
        # 创建内容区域
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        content_layout = QHBoxLayout(content_frame)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 创建侧边栏
        self.side_bar = SideBar()
        self.side_bar.setObjectName("sideBar")
        content_layout.addWidget(self.side_bar)
        
        # 创建页面堆栈
        self.stacked_widget = QStackedWidget()
        self.stacked_widget.setObjectName("contentArea")
        content_layout.addWidget(self.stacked_widget)
        
        main_layout.addWidget(content_frame)
        
        # 添加页面
        self._add_pages()
        
        # 连接信号
        self._connect_signals()
        
    def _add_pages(self):
        """添加页面到堆栈"""
        # 仪表板页面
        self.dashboard_page = DashboardPage()
        self.stacked_widget.addWidget(self.dashboard_page)
        
        # 基础配置页面
        self.basic_config_page = BasicConfigPage()
        self.stacked_widget.addWidget(self.basic_config_page)
        
        # Mod配置页面
        self.mod_config_page = ModConfigPage()
        self.stacked_widget.addWidget(self.mod_config_page)
        
        # 设置页面
        self.settings_page = SettingsPage()
        self.stacked_widget.addWidget(self.settings_page)
        
        # 默认显示仪表板
        self.stacked_widget.setCurrentWidget(self.dashboard_page)
        
    def _connect_signals(self):
        """连接信号和槽"""
        # 侧边栏页面切换
        self.side_bar.page_changed.connect(self._on_page_changed)
        
        # 标题栏按钮
        self.title_bar.minimize_clicked.connect(self.showMinimized)
        self.title_bar.maximize_clicked.connect(self._toggle_maximize)
        self.title_bar.close_clicked.connect(self.close)
        
    def _setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("ME3 Mod Manager")
        self.resize(1200, 800)
        
        # 居中显示
        self._center_window()
        
    def _center_window(self):
        """窗口居中显示"""
        screen = self.screen().availableGeometry()
        window = self.frameGeometry()
        window.moveCenter(screen.center())
        self.move(window.topLeft())
        
    def _on_page_changed(self, page_name: str):
        """页面切换处理"""
        page_map = {
            "dashboard": self.dashboard_page,
            "basic_config": self.basic_config_page,
            "mod_config": self.mod_config_page,
            "settings": self.settings_page
        }
        
        if page_name in page_map:
            self.stacked_widget.setCurrentWidget(page_map[page_name])
            self.logger.info(f"切换到页面: {page_name}")
            
    def _toggle_maximize(self):
        """切换最大化状态"""
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()
            
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件 - 用于拖动窗口"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            event.accept()
            
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 拖动窗口"""
        if event.buttons() == Qt.LeftButton and not self.drag_position.isNull():
            self.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
            
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        self.drag_position = QPoint()
        event.accept()
