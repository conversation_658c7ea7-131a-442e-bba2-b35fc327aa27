<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>BONFIRE_WARP_PARAM_ST</ParamType>
  <DataVersion>6</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>1704</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>1705</SortID>
    </Field>
    <Field Def="u32 eventflagId">
      <DisplayName>イベントフラグID</DisplayName>
      <Description>解除条件イベントフラグID</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="u32 bonfireEntityId">
      <DisplayName>篝火エンティティID</DisplayName>
      <Description>篝火エンティティID</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="dummy8 pad4[2]">
      <DisplayName>パッド</DisplayName>
      <Description>パディング。削除した旧作由来データが定義されてた場所</Description>
      <SortID>1706</SortID>
    </Field>
    <Field Def="u16 bonfireSubCategorySortId">
      <DisplayName>ソートID</DisplayName>
      <Description>篝火ワープサブカテゴリソートID。同じサブカテゴリ内の並び順（昇順）を指定する</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>340</SortID>
      <UnkC8>篝火ワープサブカテゴリ</UnkC8>
    </Field>
    <Field Def="u16 forbiddenIconId">
      <DisplayName>ワープ禁止アイコンID</DisplayName>
      <Description>ワープ禁止時のアイコンID</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>320</SortID>
    </Field>
    <Field Def="u8 dispMinZoomStep">
      <DisplayName>表示ズームステップ</DisplayName>
      <Description>篝火を表示するズームステップ（一番ズームアウトした状態が0、ズームするごとに+1）。「《表示ズームステップ》≦ 現在のズームステップ 」のときに表示される。デフォルトは 0（常に表示）</Description>
      <Maximum>128</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="u8 selectMinZoomStep">
      <DisplayName>選択可能ズームステップ</DisplayName>
      <Description>篝火を選択及びスナップ可能なズームステップ（一番ズームアウトした状態が0、ズームするごとに+1）。「《選択可能ズームステップ》≦ 現在の拡大段階 」のときに選択及びスナップ可能。デフォルトは 0（常に選択・スナップ可能）</Description>
      <Maximum>128</Maximum>
      <SortID>910</SortID>
    </Field>
    <Field Def="s32 bonfireSubCategoryId = -1">
      <DisplayName>サブカテゴリID</DisplayName>
      <Description>篝火ワープサブカテゴリパラメータID(-1:無効)。どのサブカテゴリに属するかを設定する。無効なら篝火一覧に表示されない</Description>
      <Minimum>-1</Minimum>
      <Maximum>65535</Maximum>
      <SortID>330</SortID>
      <UnkC8>篝火ワープサブカテゴリ</UnkC8>
    </Field>
    <Field Def="u32 clearedEventFlagId">
      <DisplayName>クリア済イベントフラグID</DisplayName>
      <Description>クリア済みイベントフラグID(0:常にクリア済み扱い)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>110</SortID>
    </Field>
    <Field Def="u16 iconId">
      <DisplayName>アイコンID</DisplayName>
      <Description>アイコンID</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>310</SortID>
    </Field>
    <Field Def="u8 dispMask00:1">
      <DisplayName>表示設定M00</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>M00で表示するか</Description>
      <Maximum>1</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="u8 dispMask01:1">
      <DisplayName>表示設定M01</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>M01で表示するか</Description>
      <Maximum>1</Maximum>
      <SortID>810</SortID>
    </Field>
    
    <Field Def="dummy8 pad1_old:6" RemovedVersion="11210015" />
    
    <Field Def="u8 dispMask02:1" FirstVersion="11210015" />
    <Field Def="dummy8 pad1:5" FirstVersion="11210015" >
      <DisplayName>パッド</DisplayName>
      <Description>pad1:6</Description>
      <SortID>1707</SortID>
    </Field>
    
    <Field Def="dummy8 pad2[1]">
      <DisplayName>パッド</DisplayName>
      <Description>pad2</Description>
      <SortID>1708</SortID>
    </Field>
    <Field Def="u8 areaNo">
      <DisplayName>エリア番号</DisplayName>
      <Description>mAA_BB_CC_DD の AA 部分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="u8 gridXNo">
      <DisplayName>グリッドX番号</DisplayName>
      <Description>mAA_BB_CC_DD の BB 部分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="u8 gridZNo">
      <DisplayName>グリッドZ番号</DisplayName>
      <Description>mAA_BB_CC_DD の CC 部分</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>99</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="dummy8 pad3[1]">
      <DisplayName>パディング</DisplayName>
      <Description>pad3</Description>
      <SortID>1709</SortID>
    </Field>
    <Field Def="f32 posX">
      <DisplayName>X座標</DisplayName>
      <Description>X座標</Description>
      <Minimum>-20000</Minimum>
      <Maximum>20000</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="f32 posY">
      <DisplayName>Y座標</DisplayName>
      <Description>Y座標（使っていない）</Description>
      <Minimum>-20000</Minimum>
      <Maximum>20000</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="f32 posZ">
      <DisplayName>Z座標</DisplayName>
      <Description>Z座標</Description>
      <Minimum>-20000</Minimum>
      <Maximum>20000</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="s32 textId1 = -1">
      <DisplayName>テキストID</DisplayName>
      <Description>表示するテキストID。無効値(-1)なら、何も表示しない</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>210</SortID>
      <UnkC8>テキストID01</UnkC8>
    </Field>
    <Field Def="u32 textEnableFlagId1">
      <DisplayName>出現イベントフラグID</DisplayName>
      <Description>テキストの表示イベントフラグID。イベントフラグがOnなら表示する。無効なイベントフラグID(0)なら、On扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>215</SortID>
      <UnkC8>テキストID01</UnkC8>
    </Field>
    <Field Def="u32 textDisableFlagId1">
      <DisplayName>非表示イベントフラグID</DisplayName>
      <Description>テキストの非表示イベントフラグID。イベントフラグがOnなら表示しない。表示イベントフラグIDよりも優先される。無効なイベントフラグID(0)なら、Off扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>216</SortID>
      <UnkC8>テキストID01</UnkC8>
    </Field>
    <Field Def="s32 textId2 = -1">
      <DisplayName>テキストID</DisplayName>
      <Description>表示するテキストID。無効値(-2)なら、何も表示しない</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>220</SortID>
      <UnkC8>テキストID02</UnkC8>
    </Field>
    <Field Def="u32 textEnableFlagId2">
      <DisplayName>出現イベントフラグID</DisplayName>
      <Description>テキストの表示イベントフラグID。イベントフラグがOnなら表示する。無効なイベントフラグID(1)なら、On扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>225</SortID>
      <UnkC8>テキストID02</UnkC8>
    </Field>
    <Field Def="u32 textDisableFlagId2">
      <DisplayName>非表示イベントフラグID</DisplayName>
      <Description>テキストの非表示イベントフラグID。イベントフラグがOnなら表示しない。表示イベントフラグIDよりも優先される。無効なイベントフラグID(1)なら、Off扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>226</SortID>
      <UnkC8>テキストID02</UnkC8>
    </Field>
    <Field Def="s32 textId3 = -1">
      <DisplayName>テキストID</DisplayName>
      <Description>表示するテキストID。無効値(-3)なら、何も表示しない</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>230</SortID>
      <UnkC8>テキストID03</UnkC8>
    </Field>
    <Field Def="u32 textEnableFlagId3">
      <DisplayName>出現イベントフラグID</DisplayName>
      <Description>テキストの表示イベントフラグID。イベントフラグがOnなら表示する。無効なイベントフラグID(2)なら、On扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>235</SortID>
      <UnkC8>テキストID03</UnkC8>
    </Field>
    <Field Def="u32 textDisableFlagId3">
      <DisplayName>非表示イベントフラグID</DisplayName>
      <Description>テキストの非表示イベントフラグID。イベントフラグがOnなら表示しない。表示イベントフラグIDよりも優先される。無効なイベントフラグID(2)なら、Off扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>236</SortID>
      <UnkC8>テキストID03</UnkC8>
    </Field>
    <Field Def="s32 textId4 = -1">
      <DisplayName>テキストID</DisplayName>
      <Description>表示するテキストID。無効値(-4)なら、何も表示しない</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>240</SortID>
      <UnkC8>テキストID04</UnkC8>
    </Field>
    <Field Def="u32 textEnableFlagId4">
      <DisplayName>出現イベントフラグID</DisplayName>
      <Description>テキストの表示イベントフラグID。イベントフラグがOnなら表示する。無効なイベントフラグID(3)なら、On扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>245</SortID>
      <UnkC8>テキストID04</UnkC8>
    </Field>
    <Field Def="u32 textDisableFlagId4">
      <DisplayName>非表示イベントフラグID</DisplayName>
      <Description>テキストの非表示イベントフラグID。イベントフラグがOnなら表示しない。表示イベントフラグIDよりも優先される。無効なイベントフラグID(3)なら、Off扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>246</SortID>
      <UnkC8>テキストID04</UnkC8>
    </Field>
    <Field Def="s32 textId5 = -1">
      <DisplayName>テキストID</DisplayName>
      <Description>表示するテキストID。無効値(-5)なら、何も表示しない</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>250</SortID>
      <UnkC8>テキストID05</UnkC8>
    </Field>
    <Field Def="u32 textEnableFlagId5">
      <DisplayName>出現イベントフラグID</DisplayName>
      <Description>テキストの表示イベントフラグID。イベントフラグがOnなら表示する。無効なイベントフラグID(4)なら、On扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>255</SortID>
      <UnkC8>テキストID05</UnkC8>
    </Field>
    <Field Def="u32 textDisableFlagId5">
      <DisplayName>非表示イベントフラグID</DisplayName>
      <Description>テキストの非表示イベントフラグID。イベントフラグがOnなら表示しない。表示イベントフラグIDよりも優先される。無効なイベントフラグID(4)なら、Off扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>256</SortID>
      <UnkC8>テキストID05</UnkC8>
    </Field>
    <Field Def="s32 textId6 = -1">
      <DisplayName>テキストID</DisplayName>
      <Description>表示するテキストID。無効値(-6)なら、何も表示しない</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>260</SortID>
      <UnkC8>テキストID06</UnkC8>
    </Field>
    <Field Def="u32 textEnableFlagId6">
      <DisplayName>出現イベントフラグID</DisplayName>
      <Description>テキストの表示イベントフラグID。イベントフラグがOnなら表示する。無効なイベントフラグID(5)なら、On扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>265</SortID>
      <UnkC8>テキストID06</UnkC8>
    </Field>
    <Field Def="u32 textDisableFlagId6">
      <DisplayName>非表示イベントフラグID</DisplayName>
      <Description>テキストの非表示イベントフラグID。イベントフラグがOnなら表示しない。表示イベントフラグIDよりも優先される。無効なイベントフラグID(5)なら、Off扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>266</SortID>
      <UnkC8>テキストID06</UnkC8>
    </Field>
    <Field Def="s32 textId7 = -1">
      <DisplayName>テキストID</DisplayName>
      <Description>表示するテキストID。無効値(-7)なら、何も表示しない</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>270</SortID>
      <UnkC8>テキストID07</UnkC8>
    </Field>
    <Field Def="u32 textEnableFlagId7">
      <DisplayName>出現イベントフラグID</DisplayName>
      <Description>テキストの表示イベントフラグID。イベントフラグがOnなら表示する。無効なイベントフラグID(6)なら、On扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>275</SortID>
      <UnkC8>テキストID07</UnkC8>
    </Field>
    <Field Def="u32 textDisableFlagId7">
      <DisplayName>非表示イベントフラグID</DisplayName>
      <Description>テキストの非表示イベントフラグID。イベントフラグがOnなら表示しない。表示イベントフラグIDよりも優先される。無効なイベントフラグID(6)なら、Off扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>276</SortID>
      <UnkC8>テキストID07</UnkC8>
    </Field>
    <Field Def="s32 textId8 = -1">
      <DisplayName>テキストID</DisplayName>
      <Description>表示するテキストID。無効値(-8)なら、何も表示しない</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>280</SortID>
      <UnkC8>テキストID08</UnkC8>
    </Field>
    <Field Def="u32 textEnableFlagId8">
      <DisplayName>出現イベントフラグID</DisplayName>
      <Description>テキストの表示イベントフラグID。イベントフラグがOnなら表示する。無効なイベントフラグID(7)なら、On扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>285</SortID>
      <UnkC8>テキストID08</UnkC8>
    </Field>
    <Field Def="u32 textDisableFlagId8">
      <DisplayName>非表示イベントフラグID</DisplayName>
      <Description>テキストの非表示イベントフラグID。イベントフラグがOnなら表示しない。表示イベントフラグIDよりも優先される。無効なイベントフラグID(7)なら、Off扱いされる</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>286</SortID>
      <UnkC8>テキストID08</UnkC8>
    </Field>
    <Field Def="u8 textType1">
      <DisplayName>テキスト種別</DisplayName>
      <Enum>WORLD_MAP_POINT_TEXT_TYPE</Enum>
      <Description>テキストの種別(地名,NPC名,...)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>211</SortID>
      <UnkC8>テキストID01</UnkC8>
    </Field>
    <Field Def="u8 textType2">
      <DisplayName>テキスト種別</DisplayName>
      <Enum>WORLD_MAP_POINT_TEXT_TYPE</Enum>
      <Description>テキストの種別(地名,NPC名,...)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>221</SortID>
      <UnkC8>テキストID02</UnkC8>
    </Field>
    <Field Def="u8 textType3">
      <DisplayName>テキスト種別</DisplayName>
      <Enum>WORLD_MAP_POINT_TEXT_TYPE</Enum>
      <Description>テキストの種別(地名,NPC名,...)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>231</SortID>
      <UnkC8>テキストID03</UnkC8>
    </Field>
    <Field Def="u8 textType4">
      <DisplayName>テキスト種別</DisplayName>
      <Enum>WORLD_MAP_POINT_TEXT_TYPE</Enum>
      <Description>テキストの種別(地名,NPC名,...)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>241</SortID>
      <UnkC8>テキストID04</UnkC8>
    </Field>
    <Field Def="u8 textType5">
      <DisplayName>テキスト種別</DisplayName>
      <Enum>WORLD_MAP_POINT_TEXT_TYPE</Enum>
      <Description>テキストの種別(地名,NPC名,...)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>251</SortID>
      <UnkC8>テキストID05</UnkC8>
    </Field>
    <Field Def="u8 textType6">
      <DisplayName>テキスト種別</DisplayName>
      <Enum>WORLD_MAP_POINT_TEXT_TYPE</Enum>
      <Description>テキストの種別(地名,NPC名,...)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>261</SortID>
      <UnkC8>テキストID06</UnkC8>
    </Field>
    <Field Def="u8 textType7">
      <DisplayName>テキスト種別</DisplayName>
      <Enum>WORLD_MAP_POINT_TEXT_TYPE</Enum>
      <Description>テキストの種別(地名,NPC名,...)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>271</SortID>
      <UnkC8>テキストID07</UnkC8>
    </Field>
    <Field Def="u8 textType8">
      <DisplayName>テキスト種別</DisplayName>
      <Enum>WORLD_MAP_POINT_TEXT_TYPE</Enum>
      <Description>テキストの種別(地名,NPC名,...)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <SortID>281</SortID>
      <UnkC8>テキストID08</UnkC8>
    </Field>
    <Field Def="s32 noIgnitionSfxDmypolyId_0 = -1">
      <DisplayName>点火前SFXダミポリID0</DisplayName>
      <Description>篝火点火前にSFXを出すダミポリID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="s32 noIgnitionSfxId_0 = -1">
      <DisplayName>点火前SFXID0</DisplayName>
      <Description>篝火点火前に出すSFXID。点火したら消える。-1の場合はSFXを出さない。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1701</SortID>
    </Field>
    <Field Def="s32 noIgnitionSfxDmypolyId_1 = -1">
      <DisplayName>点火前SFXダミポリID1</DisplayName>
      <Description>篝火点火前にSFXを出すダミポリID</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>1702</SortID>
    </Field>
    <Field Def="s32 noIgnitionSfxId_1 = -1">
      <DisplayName>点火前SFXID1</DisplayName>
      <Description>篝火点火前に出すSFXID。点火したら消える。-1の場合はSFXを出さない。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1703</SortID>
    </Field>
    
    <Field Def="s32 textEnableFlag2Id1" />
    <Field Def="s32 textEnableFlag2Id2" />
    <Field Def="s32 textEnableFlag2Id3" />
    <Field Def="s32 textEnableFlag2Id4" />
    <Field Def="s32 textEnableFlag2Id5" />
    <Field Def="s32 textEnableFlag2Id6" />
    <Field Def="s32 textEnableFlag2Id7" />
    <Field Def="s32 textEnableFlag2Id8" />
    <Field Def="s32 textDisableFlag2Id1" />
    <Field Def="s32 textDisableFlag2Id2" />
    <Field Def="s32 textDisableFlag2Id3" />
    <Field Def="s32 textDisableFlag2Id4" />
    <Field Def="s32 textDisableFlag2Id5" />
    <Field Def="s32 textDisableFlag2Id6" />
    <Field Def="s32 textDisableFlag2Id7" />
    <Field Def="s32 textDisableFlag2Id8" />
    <Field Def="u16 altIconId" />
    <Field Def="u16 altForbiddenIconId" />
  </Fields>
</PARAMDEF>
