<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="1">
  <ParamType>SP_EFFECT_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    
    <Field Def="s32 iconId"/>
    <Field Def="f32 conditionHp"/>
    <Field Def="f32 effectEndurance"/>
    <Field Def="f32 motionInterval"/>
    <Field Def="f32 maxHpRate"/>
    <Field Def="f32 maxMpRate"/>
    <Field Def="f32 maxStaminaRate"/>
    <Field Def="f32 slashDamageCutRate"/>
    <Field Def="f32 blowDamageCutRate"/>
    <Field Def="f32 thrustDamageCutRate"/>
    <Field Def="f32 neutralDamageCutRate"/>
    <Field Def="f32 magicDamageCutRate"/>
    <Field Def="f32 fireDamageCutRate"/>
    <Field Def="f32 thunderDamageCutRate"/>
    <Field Def="f32 physicsAttackRate"/>
    <Field Def="f32 magicAttackRate"/>
    <Field Def="f32 fireAttackRate"/>
    <Field Def="f32 thunderAttackRate"/>
    <Field Def="f32 physicsAttackPowerRate"/>
    <Field Def="f32 magicAttackPowerRate"/>
    <Field Def="f32 fireAttackPowerRate"/>
    <Field Def="f32 thunderAttackPowerRate"/>
    <Field Def="s32 physicsAttackPower"/>
    <Field Def="s32 magicAttackPower"/>
    <Field Def="s32 fireAttackPower"/>
    <Field Def="s32 thunderAttackPower"/>
    <Field Def="f32 physicsDiffenceRate"/>
    <Field Def="f32 magicDiffenceRate"/>
    <Field Def="f32 fireDiffenceRate"/>
    <Field Def="f32 thunderDiffenceRate"/>
    <Field Def="f32 physicsDiffence"/>
    <Field Def="f32 magicDiffence"/>
    <Field Def="f32 fireDiffence"/>
    <Field Def="f32 thunderDiffence"/>
    <Field Def="f32 NoGuardDamageRate"/>
    <Field Def="f32 vitalSpotChangeRate"/>
    <Field Def="f32 normalSpotChangeRate"/>
    <Field Def="f32 lookAtTargetPosOffset"/>
    <Field Def="s32 behaviorId"/>
    <Field Def="f32 changeHpRate"/>
    <Field Def="s32 changeHpPoint"/>
    <Field Def="f32 changeMpRate"/>
    <Field Def="s32 changeMpPoint"/>
    <Field Def="s32 mpRecoverChangeSpeed"/>
    <Field Def="f32 changeStaminaRate"/>
    <Field Def="s32 changeStaminaPoint"/>
    <Field Def="s32 staminaRecoverChangeSpeed"/>
    <Field Def="f32 magicEffectTimeChange"/>
    <Field Def="s32 insideDurability"/>
    <Field Def="s32 maxDurability"/>
    <Field Def="f32 staminaAttackRate"/>
    <Field Def="s32 poizonAttackPower"/>
    <Field Def="s32 diseaseAttackPower"/>
    <Field Def="s32 bloodAttackPower"/>
    <Field Def="s32 curseAttackPower"/>
    <Field Def="f32 fallDamageRate"/>
    <Field Def="f32 soulRate"/>
    <Field Def="f32 equipWeightChangeRate"/>
    <Field Def="f32 allItemWeightChangeRate"/>
    <Field Def="s32 soul"/>
    <Field Def="s32 animIdOffset"/>
    <Field Def="f32 haveSoulRate"/>
    <Field Def="f32 targetPriority"/>
    <Field Def="f32 sightSearchEnemyRate"/>
    <Field Def="f32 hearingSearchEnemyRate"/>
    <Field Def="f32 grabityRate"/>
    <Field Def="f32 registPoizonChangeRate"/>
    <Field Def="f32 registDiseaseChangeRate"/>
    <Field Def="f32 registBloodChangeRate"/>
    <Field Def="f32 registCurseChangeRate"/>
    <Field Def="s32 soulStealRate"/>
    <Field Def="f32 lifeReductionRate"/>
    <Field Def="f32 hpRecoverRate"/>
    <Field Def="s32 replaceSpEffectId"/>
    <Field Def="s32 cycleOccurrenceSpEffectId"/>
    <Field Def="s32 atkOccurrenceSpEffectId"/>
    <Field Def="f32 guardDefFlickPowerRate"/>
    <Field Def="f32 guardStaminaCutRate"/>
    <Field Def="s16 rayCastPassedTime"/>
    <Field Def="u8 magicSubCategoryChange1"/>
    <Field Def="u8 magicSubCategoryChange2"/>
    <Field Def="s16 bowDistRate"/>
    <Field Def="s16 spCategory"/>
    <Field Def="u8 categoryPriority"/>
    <Field Def="s8 saveCategory"/>
    <Field Def="u8 changeMagicSlot"/>
    <Field Def="u8 changeMiracleSlot"/>
    <Field Def="s8 heroPointDamage"/>
    <Field Def="u8 defFlickPower"/>
    <Field Def="u8 flickDamageCutRate"/>
    <Field Def="u8 bloodDamageRate"/>
    <Field Def="s8 dmgLv_None"/>
    <Field Def="s8 dmgLv_S"/>
    <Field Def="s8 dmgLv_M"/>
    <Field Def="s8 dmgLv_L"/>
    <Field Def="s8 dmgLv_BlowM"/>
    <Field Def="s8 dmgLv_Push"/>
    <Field Def="s8 dmgLv_Strike"/>
    <Field Def="s8 dmgLv_BlowS"/>
    <Field Def="s8 dmgLv_Min"/>
    <Field Def="s8 dmgLv_Uppercut"/>
    <Field Def="s8 dmgLv_BlowLL"/>
    <Field Def="s8 dmgLv_Breath"/>
    <Field Def="u8 atkAttribute"/>
    <Field Def="u8 spAttribute"/>
    <Field Def="u16 stateInfo"/>
    <Field Def="u8 wepParamChange"/>
    <Field Def="u8 moveType"/>
    <Field Def="u16 lifeReductionType"/>
    <Field Def="u8 throwCondition"/>
    <Field Def="s8 addBehaviorJudgeId_condition"/>
    <Field Def="u8 freezeDamageRate"/>
    <Field Def="u8 effectTargetSelf:1"/>
    <Field Def="u8 effectTargetFriend:1"/>
    <Field Def="u8 effectTargetEnemy:1"/>
    <Field Def="u8 effectTargetPlayer:1"/>
    <Field Def="u8 effectTargetAI:1"/>
    <Field Def="u8 effectTargetLive:1"/>
    <Field Def="u8 effectTargetGhost:1"/>
    <Field Def="u8 disableSleep:1"/>
    <Field Def="u8 disableMadness:1"/>
    <Field Def="u8 effectTargetAttacker:1"/>
    <Field Def="u8 dispIconNonactive:1"/>
    <Field Def="u8 regainGaugeDamage:1"/>
    <Field Def="u8 bAdjustMagicAblity:1"/>
    <Field Def="u8 bAdjustFaithAblity:1"/>
    <Field Def="u8 bGameClearBonus:1"/>
    <Field Def="u8 magParamChange:1"/>
    <Field Def="u8 miracleParamChange:1"/>
    <Field Def="u8 clearSoul:1"/>
    <Field Def="u8 requestSOS:1"/>
    <Field Def="u8 requestBlackSOS:1"/>
    <Field Def="u8 requestForceJoinBlackSOS:1"/>
    <Field Def="u8 requestKickSession:1"/>
    <Field Def="u8 requestLeaveSession:1"/>
    <Field Def="u8 requestNpcInveda:1"/>
    <Field Def="u8 noDead:1"/>
    <Field Def="u8 bCurrHPIndependeMaxHP:1"/>
    <Field Def="u8 corrosionIgnore:1"/>
    <Field Def="u8 sightSearchCutIgnore:1"/>
    <Field Def="u8 hearingSearchCutIgnore:1"/>
    <Field Def="u8 antiMagicIgnore:1"/>
    <Field Def="u8 fakeTargetIgnore:1"/>
    <Field Def="u8 fakeTargetIgnoreUndead:1"/>
    <Field Def="u8 fakeTargetIgnoreAnimal:1"/>
    <Field Def="u8 grabityIgnore:1"/>
    <Field Def="u8 disablePoison:1"/>
    <Field Def="u8 disableDisease:1"/>
    <Field Def="u8 disableBlood:1"/>
    <Field Def="u8 disableCurse:1"/>
    <Field Def="u8 enableCharm:1"/>
    <Field Def="u8 enableLifeTime:1"/>
    <Field Def="u8 bAdjustStrengthAblity:1"/>
    <Field Def="u8 bAdjustAgilityAblity:1"/>
    <Field Def="u8 eraseOnBonfireRecover:1"/>
    <Field Def="u8 throwAttackParamChange:1"/>
    <Field Def="u8 requestLeaveColiseumSession:1"/>
    <Field Def="u8 isExtendSpEffectLife:1"/>
    <Field Def="u8 hasTarget:1"/>
    <Field Def="u8 replanningOnFire:1"/>
    <Field Def="u8 vowType0:1"/>
    <Field Def="u8 vowType1:1"/>
    <Field Def="u8 vowType2:1"/>
    <Field Def="u8 vowType3:1"/>
    <Field Def="u8 vowType4:1"/>
    <Field Def="u8 vowType5:1"/>
    <Field Def="u8 vowType6:1"/>
    <Field Def="u8 vowType7:1"/>
    <Field Def="u8 vowType8:1"/>
    <Field Def="u8 vowType9:1"/>
    <Field Def="u8 vowType10:1"/>
    <Field Def="u8 vowType11:1"/>
    <Field Def="u8 vowType12:1"/>
    <Field Def="u8 vowType13:1"/>
    <Field Def="u8 vowType14:1"/>
    <Field Def="u8 vowType15:1"/>
    <Field Def="s8 repAtkDmgLv"/>
    <Field Def="f32 sightSearchRate"/>
    <Field Def="u8 effectTargetOpposeTarget:1"/>
    <Field Def="u8 effectTargetFriendlyTarget:1"/>
    <Field Def="u8 effectTargetSelfTarget:1"/>
    <Field Def="u8 effectTargetPcHorse:1"/>
    <Field Def="u8 effectTargetPcDeceased:1"/>
    <Field Def="u8 isContractSpEffectLife:1"/>
    <Field Def="u8 isWaitModeDelete:1"/>
    <Field Def="u8 isIgnoreNoDamage:1"/>
    <Field Def="s8 changeTeamType"/>
    <Field Def="s16 dmypolyId"/>
    <Field Def="s32 vfxId"/>
    <Field Def="s32 accumuOverFireId"/>
    <Field Def="s32 accumuOverVal"/>
    <Field Def="s32 accumuUnderFireId"/>
    <Field Def="s32 accumuUnderVal"/>
    <Field Def="s32 accumuVal"/>
    <Field Def="u8 eye_angX"/>
    <Field Def="u8 eye_angY"/>
    <Field Def="s16 addDeceasedLv"/>
    <Field Def="s32 vfxId1"/>
    <Field Def="s32 vfxId2"/>
    <Field Def="s32 vfxId3"/>
    <Field Def="s32 vfxId4"/>
    <Field Def="s32 vfxId5"/>
    <Field Def="s32 vfxId6"/>
    <Field Def="s32 vfxId7"/>
    <Field Def="s32 freezeAttackPower"/>
    <Field Def="s32 AppearAiSoundId"/>
    <Field Def="s16 addFootEffectSfxId"/>
    <Field Def="s8 dexterityCancelSystemOnlyAddDexterity"/>
    <Field Def="s8 teamOffenseEffectivity"/>
    <Field Def="f32 toughnessDamageCutRate"/>
    <Field Def="f32 weakDmgRateA"/>
    <Field Def="f32 weakDmgRateB"/>
    <Field Def="f32 weakDmgRateC"/>
    <Field Def="f32 weakDmgRateD"/>
    <Field Def="f32 weakDmgRateE"/>
    <Field Def="f32 weakDmgRateF"/>
    <Field Def="f32 darkDamageCutRate"/>
    <Field Def="f32 darkDiffenceRate"/>
    <Field Def="s32 darkDiffence"/>
    <Field Def="f32 darkAttackRate"/>
    <Field Def="f32 darkAttackPowerRate"/>
    <Field Def="s32 darkAttackPower"/>
    <Field Def="f32 antiDarkSightRadius"/>
    <Field Def="s32 antiDarkSightDmypolyId"/>
    <Field Def="f32 conditionHpRate"/>
    <Field Def="f32 consumeStaminaRate"/>
    <Field Def="f32 itemDropRate"/>
    <Field Def="s32 changePoisonResistPoint"/>
    <Field Def="s32 changeDiseaseResistPoint"/>
    <Field Def="s32 changeBloodResistPoint"/>
    <Field Def="s32 changeCurseResistPoint"/>
    <Field Def="s32 changeFreezeResistPoint"/>
    <Field Def="f32 slashAttackRate"/>
    <Field Def="f32 blowAttackRate"/>
    <Field Def="f32 thrustAttackRate"/>
    <Field Def="f32 neutralAttackRate"/>
    <Field Def="f32 slashAttackPowerRate"/>
    <Field Def="f32 blowAttackPowerRate"/>
    <Field Def="f32 thrustAttackPowerRate"/>
    <Field Def="f32 neutralAttackPowerRate"/>
    <Field Def="s32 slashAttackPower"/>
    <Field Def="s32 blowAttackPower"/>
    <Field Def="s32 thrustAttackPower"/>
    <Field Def="s32 neutralAttackPower"/>
    <Field Def="s32 unknown_144"/>
    <Field Def="f32 unknown_145"/>
    <Field Def="f32 unknown_146"/>
    <Field Def="f32 unknown_147"/>
    <Field Def="f32 unknown_148"/>
    <Field Def="s32 unknown_149"/>
    
    <Field Def="u8 madnessDamageRate"/>
    <Field Def="u8 isUseStatusAilmentAtkPowerCorrect:1"/>
    <Field Def="u8 isUseAtkParamAtkPowerCorrect:1"/>
    <Field Def="u8 dontDeleteOnDead:1"/>
    <Field Def="u8 disableFreeze:1"/>
    <Field Def="u8 isDisableNetSync:1"/>
    <Field Def="u8 shamanParamChange:1"/>
    <Field Def="u8 isStopSearchedNotify:1"/>
    <Field Def="u8 isCheckAboveShadowTest:1"/>
    <Field Def="u16 addBehaviorJudgeId_add"/>
    
    <Field Def="f32 saReceiveDamageRate"/>
    
    <Field Def="s32 unknown_old_1"/>
    <Field Def="s32 unknown_old_2"/>
    <Field Def="s32 unknown_old_3"/>
    <Field Def="s32 unknown_old_4"/>
    <Field Def="s32 unknown_old_5"/>
    
    <Field Def="f32 defEnemyDmgCorrectRate_Physics"/>
    <Field Def="f32 defEnemyDmgCorrectRate_Magic"/>
    <Field Def="f32 defEnemyDmgCorrectRate_Fire"/>
    <Field Def="f32 defEnemyDmgCorrectRate_Thunder"/>
    <Field Def="f32 defEnemyDmgCorrectRate_Dark"/>
    <Field Def="f32 defObjDmgCorrectRate"/>
    
    <Field Def="f32 unknown_old_6"/>
    <Field Def="f32 unknown_old_7"/>
    <Field Def="f32 unknown_old_8"/>
    <Field Def="f32 unknown_old_9"/>
    <Field Def="f32 unknown_old_10"/>
    
    <Field Def="f32 unknown_old_11"/>
    <Field Def="f32 unknown_old_12"/>
    <Field Def="s32 unknown_old_13"/>
    <Field Def="s32 unknown_old_14"/>
    <Field Def="s32 unknown_old_15"/>
    
    <Field Def="f32 registFreezeChangeRate"/>
    <Field Def="u16 invocationConditionsStateChange1"/>
    <Field Def="u16 invocationConditionsStateChange2"/>
    <Field Def="u16 invocationConditionsStateChange3"/>
    <Field Def="s16 hearingAiSoundLevel"/>
    
    <Field Def="f32 chrProxyHeightRate"/>
    <Field Def="f32 addAwarePointCorrectValue_forMe"/>
    <Field Def="f32 addAwarePointCorrectValue_forTarget"/>
    <Field Def="f32 sightSearchEnemyAdd"/>
    <Field Def="f32 sightSearchAdd"/>
    <Field Def="f32 hearingSearchAdd"/>
    <Field Def="f32 hearingSearchRate"/>
    <Field Def="f32 hearingSearchEnemyAdd"/>
    <Field Def="f32 value_Magnification"/>
    <Field Def="f32 artsConsumptionRate"/>
    <Field Def="f32 magicConsumptionRate"/>
    <Field Def="f32 shamanConsumptionRate"/>
    <Field Def="f32 miracleConsumptionRate"/>
    <Field Def="s32 changeHpEstusFlaskRate"/>
    <Field Def="s32 changeHpEstusFlaskPoint"/>
    <Field Def="s32 changeMpEstusFlaskRate"/>
    <Field Def="s32 changeMpEstusFlaskPoint"/>
    <Field Def="f32 changeHpEstusFlaskCorrectRate"/>
    <Field Def="f32 changeMpEstusFlaskCorrectRate"/>
    <Field Def="s32 applyIdOnGetSoul"/>
    <Field Def="f32 extendLifeRate"/>
    <Field Def="f32 contractLifeRate"/>
    <Field Def="f32 defObjectAttackPowerRate"/>
    
    <Field Def="s16 effectEndDeleteDecalGroupId"/>
    <Field Def="s8 addLifeForceStatus"/>
    <Field Def="s8 addWillpowerStatus"/>
    <Field Def="s8 addEndureStatus"/>
    <Field Def="s8 addVitalityStatus"/>
    <Field Def="s8 addStrengthStatus"/>
    <Field Def="s8 addDexterityStatus"/>
    <Field Def="s8 addMagicStatus"/>
    <Field Def="s8 addFaithStatus"/>
    <Field Def="s8 addLuckStatus"/>
    <Field Def="u8 deleteCriteriaDamage"/>
    <Field Def="u8 magicSubCategoryChange3"/>
    <Field Def="u8 spAttributeVariationValue"/>
    <Field Def="u8 atkFlickPower"/>
    <Field Def="u8 wetConditionDepth"/>
    <Field Def="f32 changeSaRecoveryVelocity"/>
    <Field Def="f32 regainRate"/>
    <Field Def="f32 saAttackPowerRate"/>
    <Field Def="s32 sleepAttackPower"/>
    <Field Def="s32 madnessAttackPower"/>
    <Field Def="f32 registSleepChangeRate"/>
    <Field Def="f32 registMadnessChangeRate"/>
    <Field Def="s32 changeSleepResistPoint"/>
    <Field Def="s32 changeMadnessResistPoint"/>
    <Field Def="u8 sleepDamageRate"/>
    <Field Def="u8 applyPartsGroup"/>
    <Field Def="u8 clearTarget:1"/>
    <Field Def="u8 fakeTargetIgnoreAjin:1"/>
    <Field Def="u8 fakeTargetIgnoreMirageArts:1"/>
    <Field Def="u8 requestForceJoinBlackSOS_B:1"/>
    <Field Def="u8 isDestinedDeathHpMult:1"/>
    <Field Def="u8 isHpBurnEffect:1"/>
    <Field Def="u8 unknown_0x352_6:1"/>
    <Field Def="u8 isPeriodicEffect:1"/>
    <Field Def="dummy8 pad2[1]"/>
    
    <Field Def="f32 changeSuperArmorPoint"/>
    <Field Def="f32 changeSaPoint"/>
    <Field Def="f32 hugeEnemyPickupHeightOverwrite"/>
    <Field Def="f32 poisonDefDamageRate"/>
    <Field Def="f32 diseaseDefDamageRate"/>
    <Field Def="f32 bloodDefDamageRate"/>
    <Field Def="f32 curseDefDamageRate"/>
    <Field Def="f32 freezeDefDamageRate"/>
    <Field Def="f32 sleepDefDamageRate"/>
    <Field Def="f32 madnessDefDamageRate"/>
    <Field Def="u16 overwrite_maxBackhomeDist"/>
    <Field Def="u16 overwrite_backhomeDist"/>
    <Field Def="u16 overwrite_backhomeBattleDist"/>
    <Field Def="u16 overwrite_BackHome_LookTargetDist"/>
    <Field Def="f32 goodsConsumptionRate"/>
    <Field Def="f32 guardStaminaMult"/>
    <Field Def="f32 ultimateArtGauge"/>
    <Field Def="f32 characterSkillCooldownReduction"/>
    <Field Def="f32 characterSkillGauge"/>
    
    <Field Def="u16 unknown_230a"/>
    <Field Def="u16 unknown_230b"/>
    <Field Def="s32 permanentBuffTextId"/>
    
    <Field Def="f32 guardCounterAttackRate"/>
    
    <Field Def="u16 triggerOnWepType"/>
    <Field Def="u8 additionalCharacterSkillUse"/>
    <Field Def="s8 triggerEffectOnKnockback_unk"/>
    
    <Field Def="u16 unknown_234a"/>
    <Field Def="u16 unknown_234b"/>
    
    <Field Def="f32 ultimateArtDuration"/>
    <Field Def="f32 characterSkillAttackRate"/>
    <Field Def="s32 improvedDodging_unk"/>
    <Field Def="s32 revenantFamily_spEffectId_1"/>
    <Field Def="s32 revenantFamily_spEffectId_2"/>
    <Field Def="s32 revenantFamily_spEffectId_3"/>
    
    <Field Def="u8 unknown_241a"/>
    <Field Def="u8 unknown_241b"/>
    <Field Def="u8 unknown_241c"/>
    <Field Def="u8 unknown_241d"/>
    
    <Field Def="dummy8 endPadding[8]"/>
  </Fields>
</PARAMDEF>