<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>MOVE_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="s32 stayId = -1">
      <DisplayName>待機</DisplayName>
      <Description>待機</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="s32 walkF = -1">
      <DisplayName>歩行 前</DisplayName>
      <Description>歩行 前</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="s32 walkB = -1">
      <DisplayName>歩行 後</DisplayName>
      <Description>歩行 後</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="s32 walkL = -1">
      <DisplayName>歩行 左</DisplayName>
      <Description>歩行 左</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="s32 walkR = -1">
      <DisplayName>歩行 右</DisplayName>
      <Description>歩行 右</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="s32 dashF = -1">
      <DisplayName>走行 前</DisplayName>
      <Description>走行 前</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="s32 dashB = -1">
      <DisplayName>走行 後</DisplayName>
      <Description>走行 後</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="s32 dashL = -1">
      <DisplayName>走行 左</DisplayName>
      <Description>走行 左</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="s32 dashR = -1">
      <DisplayName>走行 右</DisplayName>
      <Description>走行 右</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="s32 superDash = -1">
      <DisplayName>ダッシュ移動</DisplayName>
      <Description>ダッシュ移動</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="s32 escapeF = -1">
      <DisplayName>緊急回避 前</DisplayName>
      <Description>緊急回避 前</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="s32 escapeB = -1">
      <DisplayName>緊急回避 後</DisplayName>
      <Description>緊急回避 後</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="s32 escapeL = -1">
      <DisplayName>緊急回避 左</DisplayName>
      <Description>緊急回避 左</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="s32 escapeR = -1">
      <DisplayName>緊急回避 右</DisplayName>
      <Description>緊急回避 右</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="s32 turnL = -1">
      <DisplayName>90度旋回 左</DisplayName>
      <Description>90度旋回 左</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="s32 trunR = -1">
      <DisplayName>90度旋回 右</DisplayName>
      <Description>90度旋回 右</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="s32 largeTurnL = -1">
      <DisplayName>180度旋回 左</DisplayName>
      <Description>180度旋回 左</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="s32 largeTurnR = -1">
      <DisplayName>180度旋回 右</DisplayName>
      <Description>180度旋回 右</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1800</SortID>
    </Field>
    <Field Def="s32 stepMove = -1">
      <DisplayName>ステップ移動</DisplayName>
      <Description>180度旋回 右</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1900</SortID>
    </Field>
    <Field Def="s32 flyStay = -1">
      <DisplayName>飛行待機</DisplayName>
      <Description>飛行待機</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="s32 flyWalkF = -1">
      <DisplayName>飛行前進</DisplayName>
      <Description>飛行前進</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="s32 flyWalkFL = -1">
      <DisplayName>飛行左前進</DisplayName>
      <Description>飛行左前進。低回転</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2300</SortID>
    </Field>
    <Field Def="s32 flyWalkFR = -1">
      <DisplayName>飛行右前進</DisplayName>
      <Description>飛行右前進。低回転</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2400</SortID>
    </Field>
    <Field Def="s32 flyWalkFL2 = -1">
      <DisplayName>飛行左前進2</DisplayName>
      <Description>飛行左前進2。高回転</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="s32 flyWalkFR2 = -1">
      <DisplayName>飛行右前進2</DisplayName>
      <Description>飛行右前進2。高回転</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2600</SortID>
    </Field>
    <Field Def="s32 flyDashF = -1">
      <DisplayName>高速飛行前進</DisplayName>
      <Description>高速飛行前進</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2700</SortID>
    </Field>
    <Field Def="s32 flyDashFL = -1">
      <DisplayName>高速飛行左前進</DisplayName>
      <Description>高速飛行左前進。低回転</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2800</SortID>
    </Field>
    <Field Def="s32 flyDashFR = -1">
      <DisplayName>高速飛行右前進</DisplayName>
      <Description>高速飛行右前進。低回転</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>2900</SortID>
    </Field>
    <Field Def="s32 flyDashFL2 = -1">
      <DisplayName>高速飛行左前進2</DisplayName>
      <Description>高速飛行左前進2。高回転</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="s32 flyDashFR2 = -1">
      <DisplayName>高速飛行右前進2</DisplayName>
      <Description>高速飛行右前進2。高回転</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3100</SortID>
    </Field>
    <Field Def="s32 dashEscapeF = -1">
      <DisplayName>ダッシュ緊急回避前</DisplayName>
      <Description>ダッシュ緊急回避前</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3200</SortID>
    </Field>
    <Field Def="s32 dashEscapeB = -1">
      <DisplayName>ダッシュ緊急回避後</DisplayName>
      <Description>ダッシュ緊急回避後</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3300</SortID>
    </Field>
    <Field Def="s32 dashEscapeL = -1">
      <DisplayName>ダッシュ緊急回避左</DisplayName>
      <Description>ダッシュ緊急回避左</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3400</SortID>
    </Field>
    <Field Def="s32 dashEscapeR = -1">
      <DisplayName>ダッシュ緊急回避右</DisplayName>
      <Description>ダッシュ緊急回避右</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3500</SortID>
    </Field>
    <Field Def="s32 analogMoveParamId = -1">
      <DisplayName>アナログ移動パラＩＤ</DisplayName>
      <Description>移動アニメブレンドで使用される移動アニメパラメータＩＤ</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3600</SortID>
    </Field>
    <Field Def="u8 turnNoAnimAngle">
      <DisplayName>アニメなし旋回角度[deg]</DisplayName>
      <Description>旋回角度がこの値以下だと旋回アニメを再生しません（敵旋回制御のみ有効）</Description>
      <Maximum>180</Maximum>
      <SortID>3700</SortID>
    </Field>
    <Field Def="u8 turn45Angle">
      <DisplayName>45度旋回アニメ角度[deg]</DisplayName>
      <Description>旋回角度がこの値以下だと45度旋回アニメを再生します（ボス2足のみ有効）</Description>
      <Maximum>180</Maximum>
      <SortID>3800</SortID>
    </Field>
    <Field Def="u8 turn90Angle">
      <DisplayName>90度旋回アニメ角度[deg]</DisplayName>
      <Description>旋回角度がこの値以下だと90度旋回アニメを再生します（敵旋回制御のみ有効）</Description>
      <Maximum>180</Maximum>
      <SortID>3900</SortID>
    </Field>
    <Field Def="u8 turnWaitNoAnimAngle">
      <DisplayName>停止時アニメなし旋回角度[deg]</DisplayName>
      <Description>旋回角度がこの値以下だと旋回アニメを再生しません[停止時]（ボス2足のみ有効）</Description>
      <Maximum>180</Maximum>
      <SortID>4000</SortID>
    </Field>
  </Fields>
</PARAMDEF>