#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动器 - 负责ME3的参数化启动
"""

import subprocess
from pathlib import Path
from typing import Optional
from PySide6.QtCore import QObject, Signal, QProcess

from ..utils.logger import Logger
from ..utils.config import ConfigManager
from .game_manager import GameManager


class ME3Launcher(QObject):
    """ME3启动器"""
    
    # 信号定义
    launch_started = Signal()
    launch_finished = Signal(int, str)  # 退出码, 输出信息
    launch_error = Signal(str)  # 错误信息
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        self.config = ConfigManager()
        self.game_manager = GameManager()
        
        self.process = None
        
    def can_launch(self) -> tuple[bool, str]:
        """检查是否可以启动"""
        # 检查ME3是否安装
        me3_exe = Path("ME3/bin/me3.exe")
        if not me3_exe.exists():
            return False, "ME3未安装，请先下载ME3"
            
        # 检查游戏路径是否配置
        if not self.game_manager.is_game_configured():
            return False, "游戏路径未配置，请先配置游戏路径"
            
        # 检查essentials配置是否存在
        essentials_config = Path("essentials.toml")
        if not essentials_config.exists():
            return False, "Mod配置文件不存在，请先配置Mod"
            
        return True, "可以启动"
        
    def get_launch_command(self) -> Optional[list]:
        """获取启动命令"""
        try:
            # 检查是否可以启动
            can_launch, message = self.can_launch()
            if not can_launch:
                self.logger.error(f"无法启动: {message}")
                return None
                
            # 获取游戏路径
            game_path = self.game_manager.get_game_path()
            
            # 获取essentials配置路径
            essentials_config = Path("essentials.toml").absolute()
            
            # 构建启动命令
            me3_exe = Path("ME3/bin/me3.exe").absolute()
            
            command = [
                str(me3_exe),
                "launch",
                "--exe", game_path,
                "--skip-steam-init",
                "--game", "nightreign",
                "-p", str(essentials_config)
            ]
            
            self.logger.info(f"启动命令: {' '.join(command)}")
            return command
            
        except Exception as e:
            self.logger.error(f"构建启动命令失败: {e}")
            return None
            
    def launch_me3(self) -> bool:
        """启动ME3"""
        try:
            # 检查是否已有进程在运行
            if self.process and self.process.state() == QProcess.Running:
                self.logger.warning("ME3已在运行中")
                return False
                
            # 获取启动命令
            command = self.get_launch_command()
            if not command:
                return False
                
            # 创建进程
            self.process = QProcess(self)
            
            # 连接信号
            self.process.started.connect(self._on_process_started)
            self.process.finished.connect(self._on_process_finished)
            self.process.errorOccurred.connect(self._on_process_error)
            
            # 设置工作目录
            working_dir = Path.cwd()
            self.process.setWorkingDirectory(str(working_dir))
            
            # 启动进程
            program = command[0]
            arguments = command[1:]
            
            self.logger.info(f"启动ME3: {program} {' '.join(arguments)}")
            self.process.start(program, arguments)
            
            # 等待启动
            if not self.process.waitForStarted(5000):  # 5秒超时
                error_msg = f"启动超时: {self.process.errorString()}"
                self.logger.error(error_msg)
                self.launch_error.emit(error_msg)
                return False
                
            return True
            
        except Exception as e:
            error_msg = f"启动ME3失败: {e}"
            self.logger.error(error_msg)
            self.launch_error.emit(error_msg)
            return False
            
    def _on_process_started(self):
        """进程启动处理"""
        self.logger.info("ME3进程已启动")
        self.launch_started.emit()
        
    def _on_process_finished(self, exit_code: int, exit_status):
        """进程结束处理"""
        output = ""
        if self.process:
            output = self.process.readAllStandardOutput().data().decode('utf-8', errors='ignore')
            error_output = self.process.readAllStandardError().data().decode('utf-8', errors='ignore')
            
            if error_output:
                output += f"\n错误输出:\n{error_output}"
                
        self.logger.info(f"ME3进程已结束，退出码: {exit_code}")
        if output:
            self.logger.info(f"进程输出: {output}")
            
        self.launch_finished.emit(exit_code, output)
        
    def _on_process_error(self, error):
        """进程错误处理"""
        error_msg = f"ME3进程错误: {error}"
        if self.process:
            error_msg += f" - {self.process.errorString()}"
            
        self.logger.error(error_msg)
        self.launch_error.emit(error_msg)
        
    def is_running(self) -> bool:
        """检查ME3是否正在运行"""
        return self.process and self.process.state() == QProcess.Running
        
    def terminate_me3(self) -> bool:
        """终止ME3进程"""
        try:
            if not self.is_running():
                return True
                
            self.logger.info("正在终止ME3进程...")
            self.process.terminate()
            
            # 等待进程结束
            if not self.process.waitForFinished(5000):  # 5秒超时
                self.logger.warning("进程未在5秒内结束，强制杀死")
                self.process.kill()
                self.process.waitForFinished(2000)  # 再等2秒
                
            return True
            
        except Exception as e:
            self.logger.error(f"终止ME3进程失败: {e}")
            return False
            
    def get_launch_info(self) -> dict:
        """获取启动信息"""
        can_launch, message = self.can_launch()
        
        info = {
            'can_launch': can_launch,
            'message': message,
            'is_running': self.is_running(),
            'me3_installed': Path("ME3/bin/me3.exe").exists(),
            'game_configured': self.game_manager.is_game_configured(),
            'mod_configured': Path("essentials.toml").exists(),
            'command': self.get_launch_command()
        }
        
        return info
        
    def launch_with_custom_params(self, custom_params: list) -> bool:
        """使用自定义参数启动"""
        try:
            # 检查ME3是否安装
            me3_exe = Path("ME3/bin/me3.exe")
            if not me3_exe.exists():
                self.launch_error.emit("ME3未安装")
                return False
                
            # 检查是否已有进程在运行
            if self.process and self.process.state() == QProcess.Running:
                self.logger.warning("ME3已在运行中")
                return False
                
            # 创建进程
            self.process = QProcess(self)
            
            # 连接信号
            self.process.started.connect(self._on_process_started)
            self.process.finished.connect(self._on_process_finished)
            self.process.errorOccurred.connect(self._on_process_error)
            
            # 设置工作目录
            working_dir = Path.cwd()
            self.process.setWorkingDirectory(str(working_dir))
            
            # 构建完整命令
            command = [str(me3_exe.absolute())] + custom_params
            
            # 启动进程
            program = command[0]
            arguments = command[1:]
            
            self.logger.info(f"使用自定义参数启动ME3: {program} {' '.join(arguments)}")
            self.process.start(program, arguments)
            
            # 等待启动
            if not self.process.waitForStarted(5000):
                error_msg = f"启动超时: {self.process.errorString()}"
                self.logger.error(error_msg)
                self.launch_error.emit(error_msg)
                return False
                
            return True
            
        except Exception as e:
            error_msg = f"自定义启动失败: {e}"
            self.logger.error(error_msg)
            self.launch_error.emit(error_msg)
            return False
