<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>CUTSCENE_TIMEZONE_CONVERT_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 SrcTimezoneStart">
      <DisplayName>変換前時間帯開始時刻[hour]</DisplayName>
      <Description>カットシーン時刻に変換する時間帯の開始時刻[hour]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>24</Maximum>
      <Increment>0.1</Increment>
      <SortID>1</SortID>
    </Field>
    <Field Def="f32 DstCutscenTime">
      <DisplayName>変換後カットシーン時刻[hour]</DisplayName>
      <Description>カットシーン再生中に使われる時刻を時間単位で入力[hour]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>24</Maximum>
      <Increment>0.1</Increment>
      <SortID>2</SortID>
    </Field>
  </Fields>
</PARAMDEF>