<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>ESTUS_FLASK_RECOVERY_PARAM_ST</ParamType>
  <DataVersion>2</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 host">
      <DisplayName>ホスト</DisplayName>
      <Description>ホストのエスト回復数</Description>
      <SortID>1000</SortID>
    </Field>
    <Field Def="u8 invadeOrb_None">
      <DisplayName>侵入経路_オーブ_なし</DisplayName>
      <Description>侵入経路がオーブの勢力のエスト回復数</Description>
      <SortID>2000</SortID>
    </Field>
    <Field Def="u8 invadeOrb_Umbasa">
      <DisplayName>侵入経路_オーブ_太陽</DisplayName>
      <Description>侵入経路がオーブの勢力のエスト回復数</Description>
      <SortID>2100</SortID>
    </Field>
    <Field Def="u8 invadeOrb_Berserker">
      <DisplayName>侵入経路_オーブ_バーサーカー</DisplayName>
      <Description>侵入経路がオーブの勢力のエスト回復数</Description>
      <SortID>2200</SortID>
    </Field>
    <Field Def="u8 invadeOrb_Sinners">
      <DisplayName>侵入経路_オーブ_罪人</DisplayName>
      <Description>侵入経路がオーブの勢力のエスト回復数</Description>
      <SortID>2300</SortID>
    </Field>
    <Field Def="u8 invadeSign_None">
      <DisplayName>侵入経路_サイン_なし</DisplayName>
      <Description>侵入経路がサインの勢力のエスト回復数</Description>
      <SortID>3000</SortID>
    </Field>
    <Field Def="u8 invadeSign_Umbasa">
      <DisplayName>侵入経路_サイン_太陽</DisplayName>
      <Description>侵入経路がサインの勢力のエスト回復数</Description>
      <SortID>3100</SortID>
    </Field>
    <Field Def="u8 invadeSign_Berserker">
      <DisplayName>侵入経路_サイン_バーサーカー</DisplayName>
      <Description>侵入経路がサインの勢力のエスト回復数</Description>
      <SortID>3200</SortID>
    </Field>
    <Field Def="u8 invadeSign_Sinners">
      <DisplayName>侵入経路_サイン_罪人</DisplayName>
      <Description>侵入経路がサインの勢力のエスト回復数</Description>
      <SortID>3300</SortID>
    </Field>
    <Field Def="u8 invadeRing_Sinners">
      <DisplayName>侵入経路_指輪_罪人</DisplayName>
      <Description>侵入経路が指輪の勢力のエスト回復数</Description>
      <SortID>4000</SortID>
    </Field>
    <Field Def="u8 invadeRing_Rosalia">
      <DisplayName>侵入経路_指輪_ボス守(ロザリア)</DisplayName>
      <Description>侵入経路が指輪の勢力のエスト回復数</Description>
      <SortID>4100</SortID>
    </Field>
    <Field Def="u8 invadeRing_Forest">
      <DisplayName>侵入経路_指輪_マップ守(森)</DisplayName>
      <Description>侵入経路が指輪の勢力のエスト回復数</Description>
      <SortID>4200</SortID>
    </Field>
    <Field Def="u8 coopSign_None">
      <DisplayName>協力経路_サイン_なし</DisplayName>
      <Description>協力経路がサインの勢力のエスト回復数</Description>
      <SortID>5000</SortID>
    </Field>
    <Field Def="u8 coopSign_Umbasa">
      <DisplayName>協力経路_サイン_太陽</DisplayName>
      <Description>協力経路がサインの勢力のエスト回復数</Description>
      <SortID>5100</SortID>
    </Field>
    <Field Def="u8 coopSign_Berserker">
      <DisplayName>協力経路_サイン_バーサーカー</DisplayName>
      <Description>協力経路がサインの勢力のエスト回復数</Description>
      <SortID>5200</SortID>
    </Field>
    <Field Def="u8 coopSign_Sinners">
      <DisplayName>協力経路_サイン_罪人</DisplayName>
      <Description>協力経路がサインの勢力のエスト回復数</Description>
      <SortID>5300</SortID>
    </Field>
    <Field Def="u8 coopRing_RedHunter">
      <DisplayName>協力経路_指輪 _赤狩り</DisplayName>
      <Description>協力経路が指輪の勢力のエスト回復数</Description>
      <SortID>5400</SortID>
    </Field>
    <Field Def="u8 invadeRing_Anor">
      <DisplayName>侵入経路_指輪_マップ守(アノール)</DisplayName>
      <Description>侵入経路が指輪の勢力のエスト回復数</Description>
      <SortID>4250</SortID>
    </Field>
    <Field Def="u16 paramReplaceRate">
      <DisplayName>回復数パラメータ差し替え率</DisplayName>
      <Description>回復数パラメータ差し替え率</Description>
      <EditFlags>None</EditFlags>
      <Maximum>10000</Maximum>
      <SortID>10000</SortID>
    </Field>
    <Field Def="s32 paramReplaceId = -1">
      <DisplayName>回復数パラメータ差し替え先ID</DisplayName>
      <Description>回復数パラメータ差し替え先ID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>10100</SortID>
    </Field>
    <Field Def="dummy8 pad[8]">
      <SortID>10101</SortID>
    </Field>
  </Fields>
</PARAMDEF>