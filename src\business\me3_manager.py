#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ME3管理器 - 负责ME3工具的下载、安装和版本管理
"""

import requests
import zipfile
import json
from pathlib import Path
from typing import Optional, Dict, List
from PySide6.QtCore import QObject, Signal, QThread

from ..utils.logger import Logger
from ..utils.config import ConfigManager


class DownloadWorker(QThread):
    """下载工作线程"""
    
    progress_updated = Signal(int)  # 下载进度
    status_updated = Signal(str)    # 状态更新
    download_completed = Signal(bool, str)  # 下载完成 (成功, 消息)
    
    def __init__(self, url: str, output_path: Path):
        super().__init__()
        self.url = url
        self.output_path = output_path
        self.logger = Logger()
        
    def run(self):
        """执行下载"""
        try:
            self.status_updated.emit("开始下载...")
            
            # 创建输出目录
            self.output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 下载文件
            response = requests.get(self.url, stream=True, timeout=30)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(self.output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = int((downloaded_size / total_size) * 100)
                            self.progress_updated.emit(progress)
                            
            self.status_updated.emit("下载完成")
            self.download_completed.emit(True, "下载成功")
            
        except Exception as e:
            self.logger.error(f"下载失败: {e}")
            self.status_updated.emit(f"下载失败: {e}")
            self.download_completed.emit(False, str(e))


class ME3Manager(QObject):
    """ME3管理器"""
    
    # 信号定义
    download_progress = Signal(int)
    download_status = Signal(str)
    download_completed = Signal(bool, str)
    version_checked = Signal(str, str)  # 当前版本, 最新版本
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        self.config = ConfigManager()
        
        # GitHub API配置
        self.github_api_url = "https://api.github.com/repos/garyttierney/me3/releases/latest"
        self.download_mirrors = {
            "gh-proxy.com": "https://gh-proxy.com/",
            "ghproxy.net": "https://ghproxy.net/",
            "ghfast.top": "https://ghfast.top/",
            "直连 (github.com)": ""
        }
        
        self.download_worker = None
        
    def check_latest_version(self) -> Optional[Dict]:
        """检查最新版本"""
        try:
            self.logger.info("检查ME3最新版本...")
            
            response = requests.get(self.github_api_url, timeout=10)
            response.raise_for_status()
            
            release_data = response.json()
            
            # 查找Windows AMD64版本的下载链接
            download_url = None
            for asset in release_data.get('assets', []):
                if 'me3-windows-amd64.zip' in asset['name']:
                    download_url = asset['browser_download_url']
                    break
                    
            if not download_url:
                raise Exception("未找到Windows AMD64版本的下载链接")
                
            version_info = {
                'version': release_data['tag_name'],
                'name': release_data['name'],
                'published_at': release_data['published_at'],
                'download_url': download_url,
                'body': release_data.get('body', ''),
                'size': next((asset['size'] for asset in release_data['assets'] 
                            if 'me3-windows-amd64.zip' in asset['name']), 0)
            }
            
            self.logger.info(f"最新版本: {version_info['version']}")
            return version_info
            
        except Exception as e:
            self.logger.error(f"检查版本失败: {e}")
            return None
            
    def get_current_version(self) -> str:
        """获取当前安装的版本"""
        try:
            version_file = Path("ME3/version.json")
            if version_file.exists():
                with open(version_file, 'r', encoding='utf-8') as f:
                    version_data = json.load(f)
                    return version_data.get('version', 'unknown')
            return "未安装"
        except Exception as e:
            self.logger.error(f"获取当前版本失败: {e}")
            return "未知"
            
    def is_me3_installed(self) -> bool:
        """检查ME3是否已安装"""
        me3_exe = Path("ME3/bin/me3.exe")
        return me3_exe.exists()
        
    def get_download_url_with_mirror(self, original_url: str) -> str:
        """根据配置的镜像获取下载URL"""
        mirror = self.config.get('me3.download_mirror', 'gh-proxy.com')
        mirror_prefix = self.download_mirrors.get(mirror, '')
        
        if mirror_prefix:
            return mirror_prefix + original_url
        return original_url
        
    def download_me3(self, version_info: Dict) -> bool:
        """下载ME3"""
        try:
            if self.download_worker and self.download_worker.isRunning():
                self.logger.warning("下载已在进行中")
                return False
                
            # 获取下载URL
            original_url = version_info['download_url']
            download_url = self.get_download_url_with_mirror(original_url)
            
            # 设置下载路径
            download_path = Path("downloads") / "me3-windows-amd64.zip"
            
            self.logger.info(f"开始下载ME3: {download_url}")
            
            # 创建下载工作线程
            self.download_worker = DownloadWorker(download_url, download_path)
            
            # 连接信号
            self.download_worker.progress_updated.connect(self.download_progress.emit)
            self.download_worker.status_updated.connect(self.download_status.emit)
            self.download_worker.download_completed.connect(self._on_download_completed)
            
            # 开始下载
            self.download_worker.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动下载失败: {e}")
            self.download_completed.emit(False, str(e))
            return False
            
    def _on_download_completed(self, success: bool, message: str):
        """下载完成处理"""
        if success:
            try:
                # 解压文件
                self._extract_me3()
                self.download_completed.emit(True, "ME3下载并安装成功")
            except Exception as e:
                self.logger.error(f"解压ME3失败: {e}")
                self.download_completed.emit(False, f"解压失败: {e}")
        else:
            self.download_completed.emit(False, message)
            
    def _extract_me3(self):
        """解压ME3文件"""
        try:
            zip_path = Path("downloads") / "me3-windows-amd64.zip"
            extract_path = Path("ME3")
            
            self.download_status.emit("正在解压...")
            
            # 清理旧版本
            if extract_path.exists():
                import shutil
                shutil.rmtree(extract_path)
                
            # 解压文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_path)
                
            # 保存版本信息
            self._save_version_info()
            
            # 清理下载文件
            zip_path.unlink()
            
            self.logger.info("ME3解压完成")
            
        except Exception as e:
            self.logger.error(f"解压ME3失败: {e}")
            raise
            
    def _save_version_info(self):
        """保存版本信息"""
        try:
            # 从最新版本信息获取版本号
            latest_version = self.check_latest_version()
            if latest_version:
                version_data = {
                    'version': latest_version['version'],
                    'installed_at': str(Path().cwd()),
                    'download_date': latest_version['published_at']
                }
                
                version_file = Path("ME3/version.json")
                with open(version_file, 'w', encoding='utf-8') as f:
                    json.dump(version_data, f, indent=2, ensure_ascii=False)
                    
                # 更新配置
                self.config.set('me3.version', latest_version['version'])
                
        except Exception as e:
            self.logger.error(f"保存版本信息失败: {e}")
            
    def try_multiple_mirrors(self, version_info: Dict) -> bool:
        """尝试多个镜像下载"""
        original_url = version_info['download_url']
        
        for mirror_name, mirror_prefix in self.download_mirrors.items():
            try:
                self.logger.info(f"尝试使用镜像: {mirror_name}")
                self.download_status.emit(f"尝试使用镜像: {mirror_name}")
                
                download_url = mirror_prefix + original_url if mirror_prefix else original_url
                
                # 测试连接
                response = requests.head(download_url, timeout=5)
                response.raise_for_status()
                
                # 如果连接成功，开始下载
                self.config.set('me3.download_mirror', mirror_name)
                return self.download_me3(version_info)
                
            except Exception as e:
                self.logger.warning(f"镜像 {mirror_name} 连接失败: {e}")
                continue
                
        return False
