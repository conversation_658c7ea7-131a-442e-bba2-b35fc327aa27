<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>ITEMLOT_PARAM_ST</ParamType>
  <DataVersion>4</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="s32 lotItemId01">
      <DisplayName>１：アイテムID</DisplayName>
      <Description>取得できるアイテムのID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="s32 lotItemId02">
      <DisplayName>２：アイテムID</DisplayName>
      <Description>取得できるアイテムのID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1200</SortID>
    </Field>
    <Field Def="s32 lotItemId03">
      <DisplayName>３：アイテムID</DisplayName>
      <Description>取得できるアイテムのID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2000</SortID>
    </Field>
    <Field Def="s32 lotItemId04">
      <DisplayName>４：アイテムID</DisplayName>
      <Description>取得できるアイテムのID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>2800</SortID>
    </Field>
    <Field Def="s32 lotItemId05">
      <DisplayName>５：アイテムID</DisplayName>
      <Description>取得できるアイテムのID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>3600</SortID>
    </Field>
    <Field Def="s32 lotItemId06">
      <DisplayName>６：アイテムID</DisplayName>
      <Description>取得できるアイテムのID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>4400</SortID>
    </Field>
    <Field Def="s32 lotItemId07">
      <DisplayName>７：アイテムID</DisplayName>
      <Description>取得できるアイテムのID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>5200</SortID>
    </Field>
    <Field Def="s32 lotItemId08">
      <DisplayName>８：アイテムID</DisplayName>
      <Description>取得できるアイテムのID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>6000</SortID>
    </Field>
    <Field Def="s32 lotItemCategory01">
      <DisplayName>１：アイテムカテゴリ</DisplayName>
      <Enum>ITEMLOT_ITEMCATEGORY</Enum>
      <Description>取得できるアイテムのカテゴリ</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>6</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="s32 lotItemCategory02">
      <DisplayName>２：アイテムカテゴリ</DisplayName>
      <Enum>ITEMLOT_ITEMCATEGORY</Enum>
      <Description>取得できるアイテムのカテゴリ</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>6</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="s32 lotItemCategory03">
      <DisplayName>３：アイテムカテゴリ</DisplayName>
      <Enum>ITEMLOT_ITEMCATEGORY</Enum>
      <Description>取得できるアイテムのカテゴリ</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>6</Maximum>
      <SortID>1900</SortID>
    </Field>
    <Field Def="s32 lotItemCategory04">
      <DisplayName>４：アイテムカテゴリ</DisplayName>
      <Enum>ITEMLOT_ITEMCATEGORY</Enum>
      <Description>取得できるアイテムのカテゴリ</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>6</Maximum>
      <SortID>2700</SortID>
    </Field>
    <Field Def="s32 lotItemCategory05">
      <DisplayName>５：アイテムカテゴリ</DisplayName>
      <Enum>ITEMLOT_ITEMCATEGORY</Enum>
      <Description>取得できるアイテムのカテゴリ</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>6</Maximum>
      <SortID>3500</SortID>
    </Field>
    <Field Def="s32 lotItemCategory06">
      <DisplayName>６：アイテムカテゴリ</DisplayName>
      <Enum>ITEMLOT_ITEMCATEGORY</Enum>
      <Description>取得できるアイテムのカテゴリ</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>6</Maximum>
      <SortID>4300</SortID>
    </Field>
    <Field Def="s32 lotItemCategory07">
      <DisplayName>７：アイテムカテゴリ</DisplayName>
      <Enum>ITEMLOT_ITEMCATEGORY</Enum>
      <Description>取得できるアイテムのカテゴリ</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>6</Maximum>
      <SortID>5100</SortID>
    </Field>
    <Field Def="s32 lotItemCategory08">
      <DisplayName>８：アイテムカテゴリ</DisplayName>
      <Enum>ITEMLOT_ITEMCATEGORY</Enum>
      <Description>取得できるアイテムのカテゴリ</Description>
      <EditFlags>Wrap, Lock</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>6</Maximum>
      <SortID>5900</SortID>
    </Field>
    <Field Def="u16 lotItemBasePoint01">
      <DisplayName>基本出現ポイント</DisplayName>
      <Description>通常時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="u16 lotItemBasePoint02">
      <DisplayName>基本出現ポイント</DisplayName>
      <Description>通常時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="u16 lotItemBasePoint03">
      <DisplayName>基本出現ポイント</DisplayName>
      <Description>通常時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>2200</SortID>
    </Field>
    <Field Def="u16 lotItemBasePoint04">
      <DisplayName>基本出現ポイント</DisplayName>
      <Description>通常時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="u16 lotItemBasePoint05">
      <DisplayName>基本出現ポイント</DisplayName>
      <Description>通常時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>3800</SortID>
    </Field>
    <Field Def="u16 lotItemBasePoint06">
      <DisplayName>基本出現ポイント</DisplayName>
      <Description>通常時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>4600</SortID>
    </Field>
    <Field Def="u16 lotItemBasePoint07">
      <DisplayName>基本出現ポイント</DisplayName>
      <Description>通常時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>5400</SortID>
    </Field>
    <Field Def="u16 lotItemBasePoint08">
      <DisplayName>基本出現ポイント</DisplayName>
      <Description>通常時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>6200</SortID>
    </Field>
    <Field Def="u16 cumulateLotPoint01">
      <DisplayName>累積後出現ポイント</DisplayName>
      <Description>最大累積時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="u16 cumulateLotPoint02">
      <DisplayName>累積後出現ポイント</DisplayName>
      <Description>最大累積時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="u16 cumulateLotPoint03">
      <DisplayName>累積後出現ポイント</DisplayName>
      <Description>最大累積時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>2300</SortID>
    </Field>
    <Field Def="u16 cumulateLotPoint04">
      <DisplayName>累積後出現ポイント</DisplayName>
      <Description>最大累積時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>3100</SortID>
    </Field>
    <Field Def="u16 cumulateLotPoint05">
      <DisplayName>累積後出現ポイント</DisplayName>
      <Description>最大累積時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>3900</SortID>
    </Field>
    <Field Def="u16 cumulateLotPoint06">
      <DisplayName>累積後出現ポイント</DisplayName>
      <Description>最大累積時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>4700</SortID>
    </Field>
    <Field Def="u16 cumulateLotPoint07">
      <DisplayName>累積後出現ポイント</DisplayName>
      <Description>最大累積時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>5500</SortID>
    </Field>
    <Field Def="u16 cumulateLotPoint08">
      <DisplayName>累積後出現ポイント</DisplayName>
      <Description>最大累積時の出現ポイント</Description>
      <Maximum>2000</Maximum>
      <SortID>6300</SortID>
    </Field>
    <Field Def="u32 getItemFlagId01">
      <DisplayName>別ザクザクフラグID</DisplayName>
      <Description>取得済みフラグとザクザク枠兼用(0:共通使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="u32 getItemFlagId02">
      <DisplayName>別ザクザクフラグID</DisplayName>
      <Description>取得済みフラグとザクザク枠兼用(0:共通使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="u32 getItemFlagId03">
      <DisplayName>別ザクザクフラグID</DisplayName>
      <Description>取得済みフラグとザクザク枠兼用(0:共通使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>2400</SortID>
    </Field>
    <Field Def="u32 getItemFlagId04">
      <DisplayName>別ザクザクフラグID</DisplayName>
      <Description>取得済みフラグとザクザク枠兼用(0:共通使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>3200</SortID>
    </Field>
    <Field Def="u32 getItemFlagId05">
      <DisplayName>別ザクザクフラグID</DisplayName>
      <Description>取得済みフラグとザクザク枠兼用(0:共通使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>4000</SortID>
    </Field>
    <Field Def="u32 getItemFlagId06">
      <DisplayName>別ザクザクフラグID</DisplayName>
      <Description>取得済みフラグとザクザク枠兼用(0:共通使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>4800</SortID>
    </Field>
    <Field Def="u32 getItemFlagId07">
      <DisplayName>別ザクザクフラグID</DisplayName>
      <Description>取得済みフラグとザクザク枠兼用(0:共通使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>5600</SortID>
    </Field>
    <Field Def="u32 getItemFlagId08">
      <DisplayName>別ザクザクフラグID</DisplayName>
      <Description>取得済みフラグとザクザク枠兼用(0:共通使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>6400</SortID>
    </Field>
    <Field Def="u32 getItemFlagId">
      <DisplayName>ザクザクフラグID</DisplayName>
      <Description>取得済みフラグとザクザク枠兼用(0:フラグ無効)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>150</SortID>
    </Field>
    <Field Def="u32 cumulateNumFlagId">
      <DisplayName>抽選累積保存フラグID</DisplayName>
      <Description>抽選回数保存用(※8フラグ連番使用)</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>Wrap, Lock</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>160</SortID>
    </Field>
    <Field Def="u8 cumulateNumMax">
      <DisplayName>抽選累積最大数</DisplayName>
      <Description>抽選累積最大数(0:累積なし)</Description>
      <SortID>170</SortID>
    </Field>
    <Field Def="s8 lotItem_Rarity = -1">
      <DisplayName>レア度上書き</DisplayName>
      <Description>宝箱などに、どれくらい貴重なアイテムが入っているかを指定する。-1の時は上書きせず装備品パラのレア度を使用する</Description>
      <Minimum>-1</Minimum>
      <Maximum>10</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="u8 lotItemNum01">
      <DisplayName>個数</DisplayName>
      <Description>取得できるアイテムの個数</Description>
      <Maximum>99</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="u8 lotItemNum02">
      <DisplayName>個数</DisplayName>
      <Description>取得できるアイテムの個数</Description>
      <Maximum>99</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="u8 lotItemNum03">
      <DisplayName>個数</DisplayName>
      <Description>取得できるアイテムの個数</Description>
      <Maximum>99</Maximum>
      <SortID>2100</SortID>
    </Field>
    <Field Def="u8 lotItemNum04">
      <DisplayName>個数</DisplayName>
      <Description>取得できるアイテムの個数</Description>
      <Maximum>99</Maximum>
      <SortID>2900</SortID>
    </Field>
    <Field Def="u8 lotItemNum05">
      <DisplayName>個数</DisplayName>
      <Description>取得できるアイテムの個数</Description>
      <Maximum>99</Maximum>
      <SortID>3700</SortID>
    </Field>
    <Field Def="u8 lotItemNum06">
      <DisplayName>個数</DisplayName>
      <Description>取得できるアイテムの個数</Description>
      <Maximum>99</Maximum>
      <SortID>4500</SortID>
    </Field>
    <Field Def="u8 lotItemNum07">
      <DisplayName>個数</DisplayName>
      <Description>取得できるアイテムの個数</Description>
      <Maximum>99</Maximum>
      <SortID>5300</SortID>
    </Field>
    <Field Def="u8 lotItemNum08">
      <DisplayName>個数</DisplayName>
      <Description>取得できるアイテムの個数</Description>
      <Maximum>99</Maximum>
      <SortID>6100</SortID>
    </Field>
    <Field Def="u16 enableLuck01:1">
      <DisplayName>運パラメータ有効</DisplayName>
      <Enum>ITEMLOT_ENABLE_LUCK</Enum>
      <Description>抽選の確率をプレイヤーの運を反映させるか</Description>
      <Maximum>1</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="u16 enableLuck02:1">
      <DisplayName>運パラメータ有効</DisplayName>
      <Enum>ITEMLOT_ENABLE_LUCK</Enum>
      <Description>抽選の確率をプレイヤーの運を反映させるか</Description>
      <Maximum>1</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="u16 enableLuck03:1">
      <DisplayName>運パラメータ有効</DisplayName>
      <Enum>ITEMLOT_ENABLE_LUCK</Enum>
      <Description>抽選の確率をプレイヤーの運を反映させるか</Description>
      <Maximum>1</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="u16 enableLuck04:1">
      <DisplayName>運パラメータ有効</DisplayName>
      <Enum>ITEMLOT_ENABLE_LUCK</Enum>
      <Description>抽選の確率をプレイヤーの運を反映させるか</Description>
      <Maximum>1</Maximum>
      <SortID>3300</SortID>
    </Field>
    <Field Def="u16 enableLuck05:1">
      <DisplayName>運パラメータ有効</DisplayName>
      <Enum>ITEMLOT_ENABLE_LUCK</Enum>
      <Description>抽選の確率をプレイヤーの運を反映させるか</Description>
      <Maximum>1</Maximum>
      <SortID>4100</SortID>
    </Field>
    <Field Def="u16 enableLuck06:1">
      <DisplayName>運パラメータ有効</DisplayName>
      <Enum>ITEMLOT_ENABLE_LUCK</Enum>
      <Description>抽選の確率をプレイヤーの運を反映させるか</Description>
      <Maximum>1</Maximum>
      <SortID>4900</SortID>
    </Field>
    <Field Def="u16 enableLuck07:1">
      <DisplayName>運パラメータ有効</DisplayName>
      <Enum>ITEMLOT_ENABLE_LUCK</Enum>
      <Description>抽選の確率をプレイヤーの運を反映させるか</Description>
      <Maximum>1</Maximum>
      <SortID>5700</SortID>
    </Field>
    <Field Def="u16 enableLuck08:1">
      <DisplayName>運パラメータ有効</DisplayName>
      <Enum>ITEMLOT_ENABLE_LUCK</Enum>
      <Description>抽選の確率をプレイヤーの運を反映させるか</Description>
      <Maximum>1</Maximum>
      <SortID>6500</SortID>
    </Field>
    <Field Def="u16 cumulateReset01:1">
      <DisplayName>累積リセット</DisplayName>
      <Enum>ITEMLOT_CUMULATE_RESET</Enum>
      <Description>累積リセットするか</Description>
      <Maximum>1</Maximum>
      <SortID>950</SortID>
    </Field>
    <Field Def="u16 cumulateReset02:1">
      <DisplayName>累積リセット</DisplayName>
      <Enum>ITEMLOT_CUMULATE_RESET</Enum>
      <Description>累積リセットするか</Description>
      <Maximum>1</Maximum>
      <SortID>1750</SortID>
    </Field>
    <Field Def="u16 cumulateReset03:1">
      <DisplayName>累積リセット</DisplayName>
      <Enum>ITEMLOT_CUMULATE_RESET</Enum>
      <Description>累積リセットするか</Description>
      <Maximum>1</Maximum>
      <SortID>2550</SortID>
    </Field>
    <Field Def="u16 cumulateReset04:1">
      <DisplayName>累積リセット</DisplayName>
      <Enum>ITEMLOT_CUMULATE_RESET</Enum>
      <Description>累積リセットするか</Description>
      <Maximum>1</Maximum>
      <SortID>3350</SortID>
    </Field>
    <Field Def="u16 cumulateReset05:1">
      <DisplayName>累積リセット</DisplayName>
      <Enum>ITEMLOT_CUMULATE_RESET</Enum>
      <Description>累積リセットするか</Description>
      <Maximum>1</Maximum>
      <SortID>4150</SortID>
    </Field>
    <Field Def="u16 cumulateReset06:1">
      <DisplayName>累積リセット</DisplayName>
      <Enum>ITEMLOT_CUMULATE_RESET</Enum>
      <Description>累積リセットするか</Description>
      <Maximum>1</Maximum>
      <SortID>4950</SortID>
    </Field>
    <Field Def="u16 cumulateReset07:1">
      <DisplayName>累積リセット</DisplayName>
      <Enum>ITEMLOT_CUMULATE_RESET</Enum>
      <Description>累積リセットするか</Description>
      <Maximum>1</Maximum>
      <SortID>5750</SortID>
    </Field>
    <Field Def="u16 cumulateReset08:1">
      <DisplayName>累積リセット</DisplayName>
      <Enum>ITEMLOT_CUMULATE_RESET</Enum>
      <Description>累積リセットするか</Description>
      <Maximum>1</Maximum>
      <SortID>6550</SortID>
    </Field>
    <Field Def="s8 GameClearOffset = -1">
      <DisplayName>X週目以降オフセット</DisplayName>
      <Enum>ITEMLOT_ROUND_COUNT</Enum>
      <Description>周回プレイ時のオフセット</Description>
      <Minimum>-1</Minimum>
      <Maximum>7</Maximum>
      <SortID>155</SortID>
    </Field>
    <Field Def="u8 canExecByFriendlyGhost:1">
      <DisplayName>協力霊でも抽選するか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>自身が協力霊の時でも抽選するか</Description>
      <Maximum>1</Maximum>
      <SortID>180</SortID>
    </Field>
    <Field Def="u8 canExecByHostileGhost:1">
      <DisplayName>敵対霊でも抽選するか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>自身が敵対霊の時でも抽選するか</Description>
      <Maximum>1</Maximum>
      <SortID>181</SortID>
    </Field>
    <Field Def="u8 PAD1:6">
      <Description>PAD1</Description>
      <Maximum>0</Maximum>
      <Increment>0</Increment>
      <SortID>9999</SortID>
    </Field>
    
    <Field Def="dummy8 endPadding[66]" />
    
  </Fields>
</PARAMDEF>