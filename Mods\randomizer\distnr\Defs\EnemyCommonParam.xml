<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>ENEMY_COMMON_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="dummy8 reserved0[8]">
      <DisplayName>予約</DisplayName>
      <Description>(dummy8)</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <SortID>100</SortID>
    </Field>
    <Field Def="s32 soundTargetTryApproachTime">
      <DisplayName>音ターゲットに対して接近を試みる時間</DisplayName>
      <DisplayFormat>%.2f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="s32 searchTargetTryApproachTime">
      <DisplayName>索敵ターゲットに対して接近を試みる時間</DisplayName>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="s32 memoryTargetTryApproachTime">
      <DisplayName>記憶ターゲットに対して接近を試みる時間</DisplayName>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="dummy8 reserved5[40]">
      <DisplayName>予約</DisplayName>
      <Description>(dummy8)</Description>
      <DisplayFormat>%.0f</DisplayFormat>
      <SortID>1801</SortID>
    </Field>
    <Field Def="s32 activateChrByTime_PhantomId">
      <DisplayName>特定時間帯配置のエネミーの出現・消滅に演出に使うファントムシェーダID</DisplayName>
      <Description>出現・消滅の演出でファントムシェーダをフェイドする、</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="f32 findUnfavorableFailedPointDist">
      <DisplayName>パス終端まで行くと敵が見切れそうなことがわかったインタラプト、を発生させる距離</DisplayName>
      <Description>Unreachパス時、終端と対象がこの距離以内ならインタラプトを発生させる</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.1</Increment>
      <SortID>1700</SortID>
    </Field>
    <Field Def="f32 findUnfavorableFailedPointHeight">
      <DisplayName>パス終端まで行くと敵が見切れそうなことがわかったインタラプト、を発生させる高さ</DisplayName>
      <Description>Unreachパス時、終端と対象がこの距離以上ならインタラプトを発生させる</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.1</Increment>
      <SortID>1800</SortID>
    </Field>
    
    <Field Def="f32 unknown_1" />
    <Field Def="f32 unknown_2" />
    <Field Def="f32 unknown_3" />
    <Field Def="f32 unknown_4" />
    <Field Def="f32 unknown_5" />
    <Field Def="f32 unknown_6" />
    <Field Def="f32 unknown_7" />
    <Field Def="f32 unknown_8" />
    <Field Def="f32 unknown_9" />
    <Field Def="f32 unknown_10" />
    <Field Def="f32 unknown_11" />
    <Field Def="f32 unknown_12" />
    <Field Def="f32 unknown_13" />
    <Field Def="f32 unknown_14" />
    <Field Def="f32 unknown_15" />
    <Field Def="f32 unknown_16" />
    <Field Def="f32 unknown_17" />
    <Field Def="f32 unknown_18" />
    <Field Def="f32 unknown_19" />
    <Field Def="f32 unknown_20" />
    <Field Def="f32 unknown_21" />
    <Field Def="f32 unknown_22" />
    <Field Def="s32 unknown_23" />
    <Field Def="s32 unknown_24" />
    <Field Def="s32 unknown_25" />
    <Field Def="f32 unknown_26" />
    <Field Def="s32 unknown_27" />
    <Field Def="f32 unknown_28" />
    <Field Def="s32 unknown_29" />
    <Field Def="s32 unknown_30" />
    
    <Field Def="dummy8 reserved18[64]">
      <DisplayName>予約</DisplayName>
      <Description>(dummy8)</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>1802</SortID>
    </Field>
  </Fields>
</PARAMDEF>