#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仪表板页面 - 应用程序主页面，显示状态概览和快速操作
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QGroupBox, QTextEdit, QGridLayout,
                               QFrame, QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, QTimer, QDateTime
from PySide6.QtGui import QFont
from pathlib import Path

from ...utils.logger import Logger
from ...utils.config import ConfigManager
from ...business.me3_manager import ME3Manager
from ...business.launcher import ME3Launcher
from ..dialogs.download_dialog import DownloadDialog


class StatusCard(QFrame):
    """状态卡片组件"""
    
    def __init__(self, title: str, value: str, color: str = "#0078d4"):
        super().__init__()
        self._init_ui(title, value, color)
        
    def _init_ui(self, title: str, value: str, color: str):
        """初始化UI"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet(f"""
            QFrame {{
                border: 1px solid #3e3e3e;
                border-radius: 8px;
                background-color: #2d2d2d;
                padding: 10px;
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("color: #cccccc; font-size: 12px;")
        layout.addWidget(title_label)
        
        # 值
        self.value_label = QLabel(value)
        value_font = QFont()
        value_font.setPointSize(18)
        value_font.setBold(True)
        self.value_label.setFont(value_font)
        self.value_label.setStyleSheet(f"color: {color}; margin-top: 5px;")
        layout.addWidget(self.value_label)
        
    def update_value(self, value: str):
        """更新值"""
        self.value_label.setText(value)


class DashboardPage(QWidget):
    """仪表板页面"""
    
    def __init__(self):
        super().__init__()
        self.logger = Logger()
        self.config = ConfigManager()
        self.me3_manager = ME3Manager()
        self.launcher = ME3Launcher()

        self._init_ui()
        self._setup_timer()
        self._update_status()
        self._connect_signals()
        
    def _init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("仪表板")
        title_font = QFont()
        title_font.setPointSize(24)
        title_font.setBold(True)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # 状态卡片区域
        cards_layout = QGridLayout()
        cards_layout.setSpacing(20)
        
        # 游戏状态卡片
        self.game_status_card = StatusCard("游戏状态", "未配置", "#e74c3c")
        cards_layout.addWidget(self.game_status_card, 0, 0)
        
        # ME3状态卡片
        self.me3_status_card = StatusCard("ME3状态", "未安装", "#f39c12")
        cards_layout.addWidget(self.me3_status_card, 0, 1)
        
        # Mod状态卡片
        self.mod_status_card = StatusCard("已启用Mod", "0", "#27ae60")
        cards_layout.addWidget(self.mod_status_card, 0, 2)
        
        # 破解状态卡片
        self.crack_status_card = StatusCard("破解状态", "未破解", "#9b59b6")
        cards_layout.addWidget(self.crack_status_card, 1, 0)
        
        layout.addLayout(cards_layout)
        
        # 快速操作区域
        actions_group = QGroupBox("快速操作")
        actions_layout = QHBoxLayout(actions_group)
        actions_layout.setSpacing(15)
        
        # 启动游戏按钮
        self.launch_btn = QPushButton("🚀 启动游戏")
        self.launch_btn.setFixedHeight(50)
        self.launch_btn.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                background-color: #27ae60;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
            QPushButton:disabled {
                background-color: #404040;
                color: #808080;
            }
        """)
        self.launch_btn.setEnabled(False)  # 默认禁用，需要配置完成后启用
        self.launch_btn.clicked.connect(self._launch_game)
        actions_layout.addWidget(self.launch_btn)
        
        # 下载ME3按钮
        self.download_me3_btn = QPushButton("⬇️ 下载ME3")
        self.download_me3_btn.setFixedHeight(50)
        self.download_me3_btn.clicked.connect(self._show_download_dialog)
        actions_layout.addWidget(self.download_me3_btn)
        
        # 基础配置按钮
        self.config_btn = QPushButton("⚙️ 基础配置")
        self.config_btn.setFixedHeight(50)
        actions_layout.addWidget(self.config_btn)
        
        layout.addWidget(actions_group)
        
        # 日志区域
        log_group = QGroupBox("系统日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 底部弹性空间
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

    def _connect_signals(self):
        """连接信号"""
        # ME3管理器信号
        self.me3_manager.version_checked.connect(self._on_version_checked)

        # 启动器信号
        self.launcher.launch_started.connect(self._on_launch_started)
        self.launcher.launch_finished.connect(self._on_launch_finished)
        self.launcher.launch_error.connect(self._on_launch_error)

    def _show_download_dialog(self):
        """显示下载对话框"""
        try:
            download_dialog = DownloadDialog(self)
            download_dialog.download_finished.connect(self._on_download_finished)
            download_dialog.show_download_dialog()
        except Exception as e:
            self.logger.error(f"显示下载对话框失败: {e}")

    def _on_download_finished(self, success: bool):
        """下载完成处理"""
        if success:
            self.add_log_message("ME3下载完成")
            self._update_status()  # 刷新状态
        else:
            self.add_log_message("ME3下载失败")

    def _on_version_checked(self, current_version: str, latest_version: str):
        """版本检查完成"""
        if current_version != latest_version:
            self.add_log_message(f"发现新版本: {latest_version} (当前: {current_version})")

    def _launch_game(self):
        """启动游戏"""
        try:
            # 检查是否可以启动
            can_launch, message = self.launcher.can_launch()
            if not can_launch:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "无法启动", message)
                return

            # 启动游戏
            success = self.launcher.launch_me3()
            if not success:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.critical(self, "启动失败", "启动游戏失败，请查看日志获取详细信息")

        except Exception as e:
            self.logger.error(f"启动游戏失败: {e}")
            from PySide6.QtWidgets import QMessageBox
            QMessageBox.critical(self, "错误", f"启动游戏失败: {e}")

    def _on_launch_started(self):
        """启动开始处理"""
        self.add_log_message("🚀 游戏启动中...")
        self.launch_btn.setText("🔄 游戏运行中")
        self.launch_btn.setEnabled(False)

    def _on_launch_finished(self, exit_code: int, output: str):
        """启动结束处理"""
        self.add_log_message(f"🏁 游戏已退出 (退出码: {exit_code})")
        self.launch_btn.setText("🚀 启动游戏")
        self._update_launch_button_state()

    def _on_launch_error(self, error_msg: str):
        """启动错误处理"""
        self.add_log_message(f"❌ 启动错误: {error_msg}")
        self.launch_btn.setText("🚀 启动游戏")
        self._update_launch_button_state()
        
    def _setup_timer(self):
        """设置定时器更新状态"""
        self.timer = QTimer()
        self.timer.timeout.connect(self._update_status)
        self.timer.start(5000)  # 每5秒更新一次
        
    def _update_status(self):
        """更新状态信息"""
        try:
            # 检查游戏路径
            from ...business.game_manager import GameManager
            game_manager = GameManager()

            if game_manager.is_game_configured():
                self.game_status_card.update_value("已配置")
                self.game_status_card.value_label.setStyleSheet("color: #27ae60; margin-top: 5px;")
            else:
                self.game_status_card.update_value("未配置")
                self.game_status_card.value_label.setStyleSheet("color: #e74c3c; margin-top: 5px;")

            # 检查ME3状态
            me3_path = Path("ME3/bin/me3.exe")
            if me3_path.exists():
                self.me3_status_card.update_value("已安装")
                self.me3_status_card.value_label.setStyleSheet("color: #27ae60; margin-top: 5px;")
            else:
                self.me3_status_card.update_value("未安装")
                self.me3_status_card.value_label.setStyleSheet("color: #f39c12; margin-top: 5px;")

            # 检查破解状态
            if game_manager.is_cracked():
                self.crack_status_card.update_value("已破解")
                self.crack_status_card.value_label.setStyleSheet("color: #27ae60; margin-top: 5px;")
            else:
                self.crack_status_card.update_value("未破解")
                self.crack_status_card.value_label.setStyleSheet("color: #e74c3c; margin-top: 5px;")

            # 检查Mod配置状态
            essentials_config = Path("essentials.toml")
            if essentials_config.exists():
                self.mod_status_card.update_value("已配置")
                self.mod_status_card.value_label.setStyleSheet("color: #27ae60; margin-top: 5px;")
            else:
                self.mod_status_card.update_value("未配置")
                self.mod_status_card.value_label.setStyleSheet("color: #e74c3c; margin-top: 5px;")

            # 更新启动按钮状态
            self._update_launch_button_state()

        except Exception as e:
            self.logger.error(f"更新状态失败: {e}")

    def _update_launch_button_state(self):
        """更新启动按钮状态"""
        try:
            if self.launcher.is_running():
                self.launch_btn.setText("🔄 游戏运行中")
                self.launch_btn.setEnabled(False)
                return

            can_launch, message = self.launcher.can_launch()
            self.launch_btn.setEnabled(can_launch)

            if not can_launch:
                self.launch_btn.setToolTip(message)
            else:
                self.launch_btn.setToolTip("启动游戏")

        except Exception as e:
            self.logger.error(f"更新启动按钮状态失败: {e}")
            self.launch_btn.setEnabled(False)
            
    def _check_game_path(self, path: str) -> bool:
        """检查游戏路径是否有效"""
        from pathlib import Path
        return Path(path).exists() and Path(path).name == "nightreign.exe"
        
    def add_log_message(self, message: str):
        """添加日志消息"""
        self.log_text.append(f"[{QDateTime.currentDateTime().toString('hh:mm:ss')}] {message}")
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
