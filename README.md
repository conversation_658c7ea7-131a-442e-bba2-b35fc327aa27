# ME3 Mod Manager

现代化的ME3 Mod管理工具，基于PySide6开发，提供直观的图形界面来管理ME3工具和游戏Mod配置。

## 功能特性

### 🎨 现代化UI设计
- **无边框窗口**: 自定义标题栏，支持拖拽和窗口控制
- **主题切换**: 支持暗色和亮色主题
- **国际化支持**: 中文和英文界面切换
- **响应式布局**: 适配不同屏幕尺寸

### 🔧 核心功能
- **ME3工具管理**: 自动下载最新版本的ME3工具
- **镜像加速**: 支持多个GitHub镜像加速下载
- **游戏路径配置**: 智能验证游戏路径有效性
- **破解管理**: 一键应用或移除OnlineFix破解
- **Mod配置**: 可视化Mod选择和配置生成
- **参数化启动**: 使用正确参数启动ME3和游戏

### 📊 实时监控
- **状态仪表板**: 实时显示各组件状态
- **系统日志**: 详细的操作日志记录
- **进度跟踪**: 下载和操作进度可视化

## 系统要求

- **操作系统**: Windows 10/11
- **Python**: 3.8+
- **依赖**: PySide6, requests

## 安装和使用

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd Nmodm2

# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境
.venv\Scripts\activate  # Windows
# source .venv/bin/activate  # Linux/macOS

# 安装依赖
pip install PySide6 requests
```

### 2. 启动应用

```bash
# 直接启动
python main.py

# 或使用测试脚本
python test_app.py
```

### 3. 使用流程

1. **下载ME3工具**
   - 在仪表板点击"下载ME3"
   - 选择合适的镜像地址
   - 等待下载和解压完成

2. **配置游戏路径**
   - 进入"基础配置"页面
   - 点击"浏览"选择nightreign.exe文件
   - 系统会自动验证路径有效性

3. **应用破解**
   - 确保OnlineFix文件夹存在
   - 在基础配置页面点击"应用破解"
   - 系统会自动备份原文件并复制破解文件

4. **配置Mod**
   - 进入"Mod配置"页面
   - 将Mod文件放入Mods文件夹
   - 勾选要启用的Mod
   - 系统会自动生成essentials.toml配置

5. **启动游戏**
   - 返回仪表板
   - 确认所有状态为"已配置"
   - 点击"启动游戏"

## 项目结构

```
Nmodm2/
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   │   └── app.py         # 主应用程序类
│   ├── ui/                # 用户界面
│   │   ├── components/    # UI组件
│   │   ├── pages/         # 页面
│   │   ├── dialogs/       # 对话框
│   │   └── main_window.py # 主窗口
│   ├── business/          # 业务逻辑
│   │   ├── me3_manager.py # ME3管理器
│   │   ├── game_manager.py# 游戏管理器
│   │   └── launcher.py    # 启动器
│   └── utils/             # 工具模块
│       ├── logger.py      # 日志系统
│       ├── config.py      # 配置管理
│       └── theme.py       # 主题管理
├── OnlineFix/             # 破解文件目录
├── Mods/                  # Mod文件目录
├── ME3/                   # ME3工具目录
├── logs/                  # 日志文件目录
├── main.py               # 程序入口
├── test_app.py           # 测试脚本
└── README.md             # 说明文档
```

## 配置文件

### config.json
应用程序主配置文件，包含：
- 主题设置
- 语言设置
- 下载镜像配置
- 游戏路径和破解状态

### OnlineFix/gconfig.ini
游戏路径配置文件，ME3启动时会读取此文件。

### essentials.toml
ME3 Mod配置文件，由应用程序根据用户选择自动生成。

## 故障排除

### 常见问题

1. **应用程序无法启动**
   - 检查Python版本是否为3.8+
   - 确认已安装PySide6依赖
   - 查看logs目录下的日志文件

2. **ME3下载失败**
   - 尝试切换不同的镜像地址
   - 检查网络连接
   - 查看下载日志获取详细错误信息

3. **游戏启动失败**
   - 确认游戏路径正确指向nightreign.exe
   - 检查ME3工具是否正确安装
   - 验证essentials.toml配置文件是否存在

4. **破解应用失败**
   - 确认OnlineFix文件夹存在且包含破解文件
   - 检查游戏目录是否有写入权限
   - 查看日志了解具体错误原因

### 日志查看

应用程序会在`logs/`目录下生成详细的日志文件：
- `app.log`: 主要应用程序日志
- 日志文件会自动轮转，保留最近5个文件

可以在设置页面点击"打开日志文件夹"快速访问日志。

## 开发说明

### 架构设计

应用程序采用模块化设计，分离UI层和业务逻辑层：

- **UI层**: 负责用户界面展示和交互
- **业务逻辑层**: 处理具体的业务功能
- **工具层**: 提供通用的工具功能

### 扩展开发

要添加新功能：

1. 在`src/business/`下创建相应的业务逻辑模块
2. 在`src/ui/pages/`下创建或修改UI页面
3. 在主窗口中注册新页面
4. 更新配置和日志相关代码

### 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 更新日志

### v1.0.0
- 初始版本发布
- 实现所有核心功能
- 现代化UI设计
- 完整的错误处理和日志系统
