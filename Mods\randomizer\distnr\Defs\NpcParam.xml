<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>NPC_PARAM_ST</ParamType>
  <DataVersion>9</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>2000001</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>2000002</SortID>
    </Field>
    <Field Def="s32 behaviorVariationId">
      <DisplayName>行動バリエーションID</DisplayName>
      <Description>行動IDを算出するときに使用するバリエーションID.</Description>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="s32 resistCorrectId_poison = -1">
      <DisplayName>毒耐性 補正ルールID</DisplayName>
      <Description>状態異常の発動時、状態異常耐性補正パラメータの設定値を使って、一時的に最大値を変動させる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>44000</SortID>
    </Field>
    <Field Def="s32 nameId = -1">
      <DisplayName>NPC名ID</DisplayName>
      <Description>NPC名メッセージパラメータ用ID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="f32 turnVellocity">
      <DisplayName>旋回速度[deg/sec]</DisplayName>
      <Description>1秒間に旋回できる回転速度[度/秒].</Description>
      <Minimum>0</Minimum>
      <Maximum>1000000</Maximum>
      <Increment>0.1</Increment>
      <SortID>8000</SortID>
    </Field>
    <Field Def="f32 hitHeight">
      <DisplayName>対マップあたりの高さ[m]</DisplayName>
      <Description>対キャラ当たりカプセルの高さ.</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>13000</SortID>
    </Field>
    <Field Def="f32 hitRadius">
      <DisplayName>対マップあたりの半径[m]</DisplayName>
      <Description>対キャラ当たりカプセルの半径.</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>13100</SortID>
    </Field>
    <Field Def="u32 weight">
      <DisplayName>重量[kg]</DisplayName>
      <Description>重量.</Description>
      <Maximum>999999</Maximum>
      <SortID>15000</SortID>
    </Field>
    <Field Def="f32 hitYOffset">
      <DisplayName>表示位置Yオフセット[m]</DisplayName>
      <Description>モデル表示位置のY（高さ）方向のオフセット。あたり位置より浮かせることができる。</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>16000</SortID>
    </Field>
    <Field Def="u32 hp">
      <DisplayName>ＨＰ</DisplayName>
      <Description>死亡猶予.</Description>
      <Maximum>99999</Maximum>
      <SortID>21000</SortID>
    </Field>
    <Field Def="u32 mp">
      <DisplayName>ＭＰ</DisplayName>
      <Description>魔法使用量.</Description>
      <Maximum>99999</Maximum>
      <SortID>22000</SortID>
    </Field>
    <Field Def="u32 getSoul">
      <DisplayName>ソウル</DisplayName>
      <Description>死亡時に、キャラクターが取得できるソウル量.</Description>
      <Maximum>999999</Maximum>
      <SortID>27000</SortID>
    </Field>
    <Field Def="s32 itemLotId_enemy = -1">
      <DisplayName>アイテム抽選ID_エネミー用</DisplayName>
      <Description>死亡時に取得するアイテムの抽選ID_エネミー用を指定。どちらか片方のみ設定してください。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>28000</SortID>
    </Field>
    <Field Def="s32 itemLotId_map = -1">
      <DisplayName>アイテム抽選ID_マップ用</DisplayName>
      <Description>死亡時に取得するアイテムの抽選ID_マップ用を指定。どちらか片方のみ設定してください。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>28001</SortID>
    </Field>
    <Field Def="f32 maxAnkleRollAngle = -1">
      <DisplayName>FootIK足首の制限角度_ロール</DisplayName>
      <Description>FootIK足首のロールの制限角度（-1：制限なし）</Description>
      <Minimum>-1</Minimum>
      <Maximum>90</Maximum>
      <Increment>0.1</Increment>
      <SortID>200070</SortID>
    </Field>
    <Field Def="u8 chrHitGroupAndNavimesh">
      <DisplayName>あたりグループと使用ナビメッシュ</DisplayName>
      <Enum>CHR_HIT_GROUP</Enum>
      <Description>他のキャラとのあたり判定を設定（ラグドールあたりにすると他のキャラがラグドールに当たるようになる）</Description>
      <SortID>9060</SortID>
    </Field>
    <Field Def="u8 faceIconId">
      <DisplayName>NPC顔画像ID</DisplayName>
      <Description>NPC顔画像ID(0:無効値(デフォルト))。「サイン閲覧メニュー」「キックメニュー」などで表示する顔画像のIDを指定する。無効値なら着せ替えモデルを表示する</Description>
      <Maximum>99</Maximum>
      <SortID>2000000</SortID>
    </Field>
    <Field Def="s16 deactivateDist = -1">
      <DisplayName>ディアクティベート距離設定[m]</DisplayName>
      <Description>キャラがディアクティベートされる距離（オープン配置キャラのみ有効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>220</Maximum>
      <SortID>450100</SortID>
    </Field>
    
    <!-- Related to bosses -->
    <Field Def="f32 unknown_0" />
    
    <Field Def="f32 footIkErrorHeightLimit">
      <DisplayName>FootIK見た目の上下制限値</DisplayName>
      <Description>FootIK見た目の上下制限値</Description>
      <DisplayFormat>%.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>200100</SortID>
    </Field>
    <Field Def="s32 humanityLotId = -1">
      <DisplayName>人間性抽選ID</DisplayName>
      <Description>死亡時に取得する人間性の抽選IDを指定</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>33010</SortID>
    </Field>
    <Field Def="s32 spEffectID0 = -1">
      <DisplayName>常駐特殊効果0</DisplayName>
      <Description>常駐特殊効果0</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>46000</SortID>
    </Field>
    <Field Def="s32 spEffectID1 = -1">
      <DisplayName>常駐特殊効果1</DisplayName>
      <Description>常駐特殊効果1</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>47000</SortID>
    </Field>
    <Field Def="s32 spEffectID2 = -1">
      <DisplayName>常駐特殊効果2</DisplayName>
      <Description>常駐特殊効果2</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>48000</SortID>
    </Field>
    <Field Def="s32 spEffectID3 = -1">
      <DisplayName>常駐特殊効果3</DisplayName>
      <Description>常駐特殊効果3</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>49000</SortID>
    </Field>
    <Field Def="s32 spEffectID4 = -1">
      <DisplayName>常駐特殊効果4</DisplayName>
      <Description>常駐特殊効果4</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50000</SortID>
    </Field>
    <Field Def="s32 spEffectID5 = -1">
      <DisplayName>常駐特殊効果5</DisplayName>
      <Description>常駐特殊効果5</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50010</SortID>
    </Field>
    <Field Def="s32 spEffectID6 = -1">
      <DisplayName>常駐特殊効果6</DisplayName>
      <Description>常駐特殊効果6</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50020</SortID>
    </Field>
    <Field Def="s32 spEffectID7 = -1">
      <DisplayName>常駐特殊効果7</DisplayName>
      <Description>常駐特殊効果7</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50030</SortID>
    </Field>
    <Field Def="s32 GameClearSpEffectID = -1">
      <DisplayName>周回ボーナス用特殊効果ＩＤ</DisplayName>
      <Description>周回ボーナス用特殊効果ＩＤ</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>51000</SortID>
    </Field>
    <Field Def="f32 physGuardCutRate">
      <DisplayName>物理攻撃カット率[％]</DisplayName>
      <Description>ガード時のダメージカット率を各攻撃ごとに設定</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>55000</SortID>
    </Field>
    <Field Def="f32 magGuardCutRate">
      <DisplayName>魔法攻撃カット率[％]</DisplayName>
      <Description>ガード攻撃でない場合は、0を入れる</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>60000</SortID>
    </Field>
    <Field Def="f32 fireGuardCutRate">
      <DisplayName>炎攻撃力カット率[％]</DisplayName>
      <Description>炎攻撃をどれだけカットするか？</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>61000</SortID>
    </Field>
    <Field Def="f32 thunGuardCutRate">
      <DisplayName>電撃攻撃力カット率[％]</DisplayName>
      <Description>電撃攻撃をどれだけカットするか？</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>61100</SortID>
    </Field>
    <Field Def="s32 animIdOffset">
      <DisplayName>アニメIDオフセット1</DisplayName>
      <Description>すべてのアニメをこの数だけずらしたIDで再生します。なければ元のアニメIDを参照します。</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>83000</SortID>
    </Field>
    <Field Def="s16 lockGazePoint0 = -1">
      <DisplayName>ロックダミポリ0の注視点</DisplayName>
      <Description>ロックオンダミポリ22Xをロックしている際に指定したダミポリを注視する（-1：無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>10600</SortID>
    </Field>
    <Field Def="s16 lockGazePoint1 = -1">
      <DisplayName>ロックダミポリ1の注視点</DisplayName>
      <Description>ロックオンダミポリ22Xをロックしている際に指定したダミポリを注視する（-1：無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>10610</SortID>
    </Field>
    <Field Def="s16 lockGazePoint2 = -1">
      <DisplayName>ロックダミポリ2の注視点</DisplayName>
      <Description>ロックオンダミポリ22Xをロックしている際に指定したダミポリを注視する（-1：無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>10620</SortID>
    </Field>
    <Field Def="s16 lockGazePoint3 = -1">
      <DisplayName>ロックダミポリ3の注視点</DisplayName>
      <Description>ロックオンダミポリ22Xをロックしている際に指定したダミポリを注視する（-1：無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>10630</SortID>
    </Field>
    <Field Def="s16 lockGazePoint4 = -1">
      <DisplayName>ロックダミポリ4の注視点</DisplayName>
      <Description>ロックオンダミポリ22Xをロックしている際に指定したダミポリを注視する（-1：無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>10640</SortID>
    </Field>
    <Field Def="s16 lockGazePoint5 = -1">
      <DisplayName>ロックダミポリ5の注視点</DisplayName>
      <Description>ロックオンダミポリ22Xをロックしている際に指定したダミポリを注視する（-1：無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>10650</SortID>
    </Field>
    <Field Def="f32 networkWarpDist">
      <DisplayName>ネットワークワープ判定距離[m/秒]</DisplayName>
      <Description>ネットワークの同期で、補完移動でなくワープさせる距離。スピードの速い人（exドラゴン)は長めにしてあげる必要がある。</Description>
      <Minimum>0</Minimum>
      <Maximum>1000</Maximum>
      <Increment>0.1</Increment>
      <SortID>101000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorR1 = -1">
      <DisplayName>R1</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>102000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorL1 = -1">
      <DisplayName>L1</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>103000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorR2 = -1">
      <DisplayName>R2</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>104000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorL2 = -1">
      <DisplayName>L2</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>105000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorRL = -1">
      <DisplayName>□</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>106000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorRR = -1">
      <DisplayName>○</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>107000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorRD = -1">
      <DisplayName>×</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>108000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorRU = -1">
      <DisplayName>△</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>109000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorLL = -1">
      <DisplayName>←</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>110000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorLR = -1">
      <DisplayName>→</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>111000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorLD = -1">
      <DisplayName>↓</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>112000</SortID>
    </Field>
    <Field Def="s32 dbgBehaviorLU = -1">
      <DisplayName>↑</DisplayName>
      <Description>行動パラメータツールからIDを登録し、行動を指定する.</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>113000</SortID>
    </Field>
    <Field Def="s32 animIdOffset2">
      <DisplayName>アニメIDオフセット2</DisplayName>
      <Description>すべてのアニメをこの数だけずらしたIDで再生します。なければアニメIDオフセット1のアニメIDを参照します。</Description>
      <Minimum>-1</Minimum>
      <Maximum>99999999</Maximum>
      <SortID>122000</SortID>
    </Field>
    <Field Def="f32 partsDamageRate1 = 1">
      <DisplayName>ダメージグループ1ダメージ倍率</DisplayName>
      <Description>部位1に対するダメージ処理に適応する倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>51110</SortID>
    </Field>
    <Field Def="f32 partsDamageRate2 = 1">
      <DisplayName>ダメージグループ2ダメージ倍率</DisplayName>
      <Description>部位2に対するダメージ処理に適応する倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>51120</SortID>
    </Field>
    <Field Def="f32 partsDamageRate3 = 1">
      <DisplayName>ダメージグループ3ダメージ倍率</DisplayName>
      <Description>部位3に対するダメージ処理に適応する倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>51130</SortID>
    </Field>
    <Field Def="f32 partsDamageRate4 = 1">
      <DisplayName>ダメージグループ4ダメージ倍率</DisplayName>
      <Description>部位4に対するダメージ処理に適応する倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>51140</SortID>
    </Field>
    <Field Def="f32 partsDamageRate5 = 1">
      <DisplayName>ダメージグループ5ダメージ倍率</DisplayName>
      <Description>部位5に対するダメージ処理に適応する倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>51150</SortID>
    </Field>
    <Field Def="f32 partsDamageRate6 = 1">
      <DisplayName>ダメージグループ6ダメージ倍率</DisplayName>
      <Description>部位6に対するダメージ処理に適応する倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>51160</SortID>
    </Field>
    <Field Def="f32 partsDamageRate7 = 1">
      <DisplayName>ダメージグループ7ダメージ倍率</DisplayName>
      <Description>部位7に対するダメージ処理に適応する倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>51165</SortID>
    </Field>
    <Field Def="f32 partsDamageRate8 = 1">
      <DisplayName>ダメージグループ8ダメージ倍率</DisplayName>
      <Description>部位8に対するダメージ処理に適応する倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>51166</SortID>
    </Field>
    <Field Def="f32 weakPartsDamageRate = 1">
      <DisplayName>弱点部位ダメージ倍率</DisplayName>
      <Description>弱点部位に対するダメージ処理に適応する倍率</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>51170</SortID>
    </Field>
    <Field Def="f32 superArmorRecoverCorrection">
      <DisplayName>SA回復時間補正値</DisplayName>
      <Description>スーパーアーマー回復時間用の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>-1</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>39200</SortID>
    </Field>
    <Field Def="f32 superArmorBrakeKnockbackDist">
      <DisplayName>SAブレイク時ノックバック距離</DisplayName>
      <Description>SAブレイクの時だけに使えるノックバック距離</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>41110</SortID>
    </Field>
    <Field Def="u16 stamina">
      <DisplayName>スタミナ</DisplayName>
      <Description>スタミナ総量.</Description>
      <Maximum>999</Maximum>
      <SortID>24000</SortID>
    </Field>
    <Field Def="u16 staminaRecoverBaseVel">
      <DisplayName>スタミナ回復基本速度[point/s]</DisplayName>
      <Description>スタミナ回復基本速度[point/s]</Description>
      <Maximum>1000</Maximum>
      <SortID>25000</SortID>
    </Field>
    <Field Def="u16 def_phys">
      <DisplayName>物理防御力</DisplayName>
      <Description>物理攻撃に対するダメージ減少基本値.</Description>
      <Maximum>9999</Maximum>
      <SortID>35000</SortID>
    </Field>
    <Field Def="s16 def_slash">
      <DisplayName>斬撃防御力[％]</DisplayName>
      <Description>攻撃属性を見て、斬撃属性のときは、防御力を減少させる.</Description>
      <Minimum>-100</Minimum>
      <Maximum>999</Maximum>
      <SortID>35100</SortID>
    </Field>
    <Field Def="s16 def_blow">
      <DisplayName>打撃防御力[％]</DisplayName>
      <Description>攻撃属性を見て、打撃属性のときは、防御力を減少させる.</Description>
      <Minimum>-100</Minimum>
      <Maximum>999</Maximum>
      <SortID>35200</SortID>
    </Field>
    <Field Def="s16 def_thrust">
      <DisplayName>刺突防御力[％]</DisplayName>
      <Description>攻撃属性を見て、刺突属性のときは、防御力を減少させる.</Description>
      <Minimum>-100</Minimum>
      <Maximum>999</Maximum>
      <SortID>35300</SortID>
    </Field>
    <Field Def="u16 def_mag">
      <DisplayName>魔法防御力</DisplayName>
      <Description>魔法攻撃に対するダメージ減少基本値.</Description>
      <Maximum>9999</Maximum>
      <SortID>35400</SortID>
    </Field>
    <Field Def="u16 def_fire">
      <DisplayName>炎防御力</DisplayName>
      <Description>炎攻撃に対するダメージ減少基本値.</Description>
      <Maximum>9999</Maximum>
      <SortID>35500</SortID>
    </Field>
    <Field Def="u16 def_thunder">
      <DisplayName>電撃防御力</DisplayName>
      <Description>電撃攻撃に対するダメージ減少基本値.</Description>
      <Maximum>9999</Maximum>
      <SortID>35600</SortID>
    </Field>
    <Field Def="u16 defFlickPower">
      <DisplayName>はじき防御力</DisplayName>
      <Description>敵の攻撃のはじき判定に使用。//ガード以外の通常攻撃でもはじけるようにするためのものです.//硬い表皮の敵は、何もしなくてもはじかれることがある…みたいな感じ通常の敵なら関係ないです.</Description>
      <Maximum>999</Maximum>
      <SortID>40000</SortID>
    </Field>
    <Field Def="u16 resist_poison">
      <DisplayName>毒耐性</DisplayName>
      <Description>毒状態異常へのかかりにくさ</Description>
      <Maximum>999</Maximum>
      <SortID>43000</SortID>
    </Field>
    <Field Def="u16 resist_desease">
      <DisplayName>疫病耐性</DisplayName>
      <Description>疫病状態異常へのかかりにくさ</Description>
      <Maximum>999</Maximum>
      <SortID>43100</SortID>
    </Field>
    <Field Def="u16 resist_blood">
      <DisplayName>出血耐性</DisplayName>
      <Description>出血状態異常へのかかりにくさ</Description>
      <Maximum>999</Maximum>
      <SortID>43200</SortID>
    </Field>
    <Field Def="u16 resist_curse">
      <DisplayName>呪耐性</DisplayName>
      <Description>呪状態異常へのかかりにくさ</Description>
      <Maximum>999</Maximum>
      <SortID>43300</SortID>
    </Field>
    <Field Def="s16 ghostModelId = -1">
      <DisplayName>徘徊ゴースト時差し替えモデルID</DisplayName>
      <Description>徘徊ゴースト化したときの差し替えモデル、テクスチャID</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>99000</SortID>
    </Field>
    <Field Def="s16 normalChangeResouceId = -1">
      <DisplayName>通常時差し替えリソースID</DisplayName>
      <Description>通常時のリソースID差し替え（むやみに使わないこと）</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>100000</SortID>
    </Field>
    <Field Def="s16 guardAngle">
      <DisplayName>ガード範囲[deg]</DisplayName>
      <Description>武器のガード時の防御発生範囲角度.保留中</Description>
      <Minimum>0</Minimum>
      <Maximum>180</Maximum>
      <SortID>54000</SortID>
    </Field>
    <Field Def="s16 slashGuardCutRate">
      <DisplayName>斬撃攻撃カット率[％]</DisplayName>
      <Description>攻撃タイプを見て、斬撃属性のダメージを何％カットするか？を指定</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <SortID>56000</SortID>
    </Field>
    <Field Def="s16 blowGuardCutRate">
      <DisplayName>打撃攻撃カット率[％]</DisplayName>
      <Description>攻撃タイプを見て、打撃属性のダメージを何％カットするか？を指定</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <SortID>57000</SortID>
    </Field>
    <Field Def="s16 thrustGuardCutRate">
      <DisplayName>刺突攻撃カット率[％]</DisplayName>
      <Description>攻撃タイプを見て、刺突属性のダメージを何％カットするか？を指定</Description>
      <Minimum>-100</Minimum>
      <Maximum>100</Maximum>
      <SortID>58000</SortID>
    </Field>
    <Field Def="s16 lockGazePoint6 = -1">
      <DisplayName>ロックダミポリ6の注視点</DisplayName>
      <Description>ロックオンダミポリ22Xをロックしている際に指定したダミポリを注視する（-1：無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>10660</SortID>
    </Field>
    <Field Def="s16 normalChangeTexChrId = -1">
      <DisplayName>通常時差し替えテクスチャキャラID</DisplayName>
      <Description>通常時差し替えテクスチャキャラID（むやみに使わないこと）</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>100100</SortID>
    </Field>
    <Field Def="u16 dropType">
      <DisplayName>ドロップアイテムの表示形式</DisplayName>
      <Enum>NPC_ITEMDROP_TYPE</Enum>
      <Description>アイテムドロップ時の表示方法(死体発光orアイテム表示)</Description>
      <Maximum>1</Maximum>
      <SortID>26000</SortID>
    </Field>
    <Field Def="u8 knockbackRate">
      <DisplayName>ノックバックカット率[％]</DisplayName>
      <Description>ノックバックダメージを受けたときの減少値／具体的には、攻撃側のノックバック初速度をカットする</Description>
      <Maximum>100</Maximum>
      <SortID>41000</SortID>
    </Field>
    <Field Def="u8 knockbackParamId">
      <DisplayName>ノックバックパラメータID</DisplayName>
      <Description>ノックバック時に使用するパラメータIDを設定</Description>
      <SortID>41100</SortID>
    </Field>
    <Field Def="u8 fallDamageDump">
      <DisplayName>落下ダメージ軽減補正[％]</DisplayName>
      <Description>落下ダメージ軽減補正[％]</Description>
      <Maximum>100</Maximum>
      <SortID>42000</SortID>
    </Field>
    <Field Def="u8 staminaGuardDef">
      <DisplayName>スタミナ攻撃カット率[％]</DisplayName>
      <Description>ガード成功時に、敵のスタミナ攻撃に対する防御力</Description>
      <Maximum>100</Maximum>
      <SortID>66000</SortID>
    </Field>
    <Field Def="u16 resist_sleep">
      <DisplayName>睡眠耐性</DisplayName>
      <Description>睡眠状態異常へのかかりにくさ</Description>
      <Maximum>999</Maximum>
      <SortID>43500</SortID>
    </Field>
    <Field Def="u16 resist_madness">
      <DisplayName>発狂耐性</DisplayName>
      <Description>発狂状態異常へのかかりにくさ</Description>
      <Maximum>999</Maximum>
      <SortID>43600</SortID>
    </Field>
    <Field Def="s8 sleepGuardResist">
      <DisplayName>睡眠攻撃カット率[％]</DisplayName>
      <Description>睡眠に対する攻撃力（特殊効果パラメータに設定）をどれだけカットするか</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>63500</SortID>
    </Field>
    <Field Def="s8 madnessGuardResist">
      <DisplayName>発狂攻撃カット率[％]</DisplayName>
      <Description>発狂に対する攻撃力（特殊効果パラメータに設定）をどれだけカットするか</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>63600</SortID>
    </Field>
    <Field Def="s16 lockGazePoint7 = -1">
      <DisplayName>ロックダミポリ7の注視点</DisplayName>
      <Description>ロックオンダミポリ22Xをロックしている際に指定したダミポリを注視する（-1：無効）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>10670</SortID>
    </Field>
    <Field Def="u8 mpRecoverBaseVel">
      <DisplayName>MP回復基本速度[％/s]</DisplayName>
      <Description>MP回復基本速度[％/s]</Description>
      <Maximum>100</Maximum>
      <SortID>23000</SortID>
    </Field>
    <Field Def="u8 flickDamageCutRate">
      <DisplayName>はじき時ダメージ減衰率[%]</DisplayName>
      <Description>攻撃をはじいた時にダメージを減衰する値を設定</Description>
      <Maximum>100</Maximum>
      <SortID>40100</SortID>
    </Field>
    <Field Def="s8 defaultLodParamId = -1">
      <DisplayName>デフォルトLODパラムID</DisplayName>
      <Description>デフォルトLODパラムID(-1：なし)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <SortID>200000</SortID>
    </Field>
    <Field Def="s8 drawType">
      <DisplayName>描画タイプ</DisplayName>
      <Enum>NPC_DRAW_TYPE</Enum>
      <Description>描画タイプ</Description>
      <Minimum>0</Minimum>
      <Maximum>255</Maximum>
      <SortID>4000</SortID>
    </Field>
    <Field Def="u8 npcType">
      <DisplayName>NPCタイプ</DisplayName>
      <Enum>NPC_TYPE</Enum>
      <Description>NPCの種類.ザコ敵/ボス敵が区別されていればOK</Description>
      <SortID>5000</SortID>
    </Field>
    <Field Def="u8 teamType">
      <DisplayName>チームタイプ</DisplayName>
      <Enum>TEAM_TYPE</Enum>
      <Description>NPCの攻撃が当たる/当たらない、狙う/狙わない設定</Description>
      <SortID>6000</SortID>
    </Field>
    <Field Def="u8 moveType">
      <DisplayName>移動タイプ</DisplayName>
      <Enum>NPC_MOVE_TYPE</Enum>
      <Description>移動方法。これにより制御が変更される.</Description>
      <SortID>7000</SortID>
    </Field>
    <Field Def="u8 lockDist">
      <DisplayName>ロック距離</DisplayName>
      <Description>ロックオンできる距離[m]</Description>
      <SortID>10000</SortID>
    </Field>
    <Field Def="u16 materialSe_Weak1">
      <DisplayName>弱点防御材質1【SE】</DisplayName>
      <Enum>WEP_MATERIAL_DEF</Enum>
      <Description>弱点部位ダメージを受けた時に鳴らすSEを判定する。1</Description>
      <Maximum>9999</Maximum>
      <SortID>51200</SortID>
    </Field>
    <Field Def="u16 materialSfx_Weak1">
      <DisplayName>弱点防御材質1【SFX】</DisplayName>
      <Enum>WEP_MATERIAL_DEF_SFX</Enum>
      <Description>弱点部位ダメージを受けた時に発生するSFXを判定する。1</Description>
      <Maximum>9999</Maximum>
      <SortID>51300</SortID>
    </Field>
    <Field Def="u8 partsDamageType">
      <DisplayName>部位ダメージ適用攻撃</DisplayName>
      <Enum>ATK_PARAM_PARTSDMGTYPE</Enum>
      <Description>部位ダメージを適用する攻撃タイプを設定する</Description>
      <Maximum>2</Maximum>
      <SortID>51100</SortID>
    </Field>
    <Field Def="u8 vowType">
      <DisplayName>誓約</DisplayName>
      <Enum>VOW_TYPE</Enum>
      <Description>誓約タイプ</Description>
      <Maximum>15</Maximum>
      <SortID>125000</SortID>
    </Field>
    <Field Def="s8 guardLevel">
      <DisplayName>ガードレベル</DisplayName>
      <Description>ガードしたとき、敵の攻撃をどのガードモーションで受けるか？を決める</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>52000</SortID>
    </Field>
    <Field Def="u8 burnSfxType">
      <DisplayName>燃焼SFXタイプ</DisplayName>
      <Enum>NPC_BURN_TYPE</Enum>
      <Description>燃焼時のSFXタイプ</Description>
      <SortID>53000</SortID>
    </Field>
    <Field Def="s8 poisonGuardResist">
      <DisplayName>毒耐性カット率[％]</DisplayName>
      <Description>毒にする攻撃力（特殊効果パラメータに設定）をどれだけカットするか</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>63000</SortID>
    </Field>
    <Field Def="s8 diseaseGuardResist">
      <DisplayName>疫病攻撃カット率[％]</DisplayName>
      <Description>疫病にする攻撃力（特殊効果パラメータに設定）をどれだけカットするか</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>63100</SortID>
    </Field>
    <Field Def="s8 bloodGuardResist">
      <DisplayName>出血攻撃カット率[％]</DisplayName>
      <Description>出血にする攻撃力（特殊効果パラメータに設定）をどれだけカットするか</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>63200</SortID>
    </Field>
    <Field Def="s8 curseGuardResist">
      <DisplayName>呪攻撃カット率[％]</DisplayName>
      <Description>呪にする攻撃力（特殊効果パラメータに設定）をどれだけカットするか</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>63300</SortID>
    </Field>
    <Field Def="u8 parryAttack">
      <DisplayName>パリィ攻撃力</DisplayName>
      <Description>パリィ攻撃力。パリィする側が使用</Description>
      <SortID>72000</SortID>
    </Field>
    <Field Def="u8 parryDefence">
      <DisplayName>パリィ防御力</DisplayName>
      <Description>パリィ防御力。パリィされる側が使用。</Description>
      <SortID>73000</SortID>
    </Field>
    <Field Def="u8 sfxSize">
      <DisplayName>SFXサイズ</DisplayName>
      <Enum>NPC_SFX_SIZE</Enum>
      <Description>SFXサイズ</Description>
      <SortID>97000</SortID>
    </Field>
    <Field Def="u8 pushOutCamRegionRadius = 12">
      <DisplayName>カメラ押し出し領域半径[m]</DisplayName>
      <Description>カメラ押し出し領域半径[m]</Description>
      <EditFlags>None</EditFlags>
      <SortID>98000</SortID>
    </Field>
    <Field Def="u8 hitStopType">
      <DisplayName>ヒットストップするか</DisplayName>
      <Enum>NPC_HITSTOP_TYPE</Enum>
      <Description>ヒットストップ処理を行うかどうかの設定</Description>
      <SortID>9510</SortID>
    </Field>
    <Field Def="u8 ladderEndChkOffsetTop = 15">
      <DisplayName>はしご上終端オフセット[1/10m]</DisplayName>
      <Description>はしご終端判定用オフセット上側</Description>
      <SortID>16500</SortID>
    </Field>
    <Field Def="u8 ladderEndChkOffsetLow = 8">
      <DisplayName>はしご下終端オフセット[1/10m]</DisplayName>
      <Description>はしご終端判定用オフセット下側</Description>
      <SortID>16600</SortID>
    </Field>
    <Field Def="u8 useRagdollCamHit:1">
      <DisplayName>カメラヒットあたりラグドール</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>敵のラグドールにカメラがあたるか。(プレイヤにも当たるときのみ有効)</Description>
      <Maximum>1</Maximum>
      <SortID>9100</SortID>
    </Field>
    <Field Def="u8 disableClothRigidHit:1">
      <DisplayName>クロスリジッドヒットを無効</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>クロスリジッドが自分に当たらないようにしたければ○</Description>
      <Maximum>1</Maximum>
      <SortID>9500</SortID>
    </Field>
    <Field Def="u8 useUndulationAddAnimFB:1">
      <DisplayName>前後の起伏加算を使用するか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>前後の起伏加算を使用するか</Description>
      <Maximum>1</Maximum>
      <SortID>20200</SortID>
    </Field>
    <Field Def="u8 isWeakA:1">
      <DisplayName>特攻Aか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>特攻Aか。特攻Aダメージ倍率が計算に含まれるようになります</Description>
      <Maximum>1</Maximum>
      <SortID>11000</SortID>
    </Field>
    <Field Def="u8 isGhost:1">
      <DisplayName>霊体か</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>相手の攻撃がすり抜けるようになります。武器パラの「対霊武器」が○の武器で攻撃された時のみ攻撃が当たります。徘徊ゴーストと混同しないように注意</Description>
      <Maximum>1</Maximum>
      <SortID>11010</SortID>
    </Field>
    <Field Def="u8 isNoDamageMotion:1">
      <DisplayName>ダメージ0のときにダメージモーションなしか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ダメージ0のときにダメージモーションを再生しないか。</Description>
      <Maximum>1</Maximum>
      <SortID>12000</SortID>
    </Field>
    <Field Def="u8 isUnduration:1">
      <DisplayName>起伏に角度をあわせるか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>キャラの前後回転を地面の起伏に合わせるか。飛行キャラの場合は使用不可</Description>
      <Maximum>1</Maximum>
      <SortID>19000</SortID>
    </Field>
    <Field Def="u8 isChangeWanderGhost:1">
      <DisplayName>徘徊ゴーストになるか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>プレイヤーがクライアントのときに徘徊ゴーストになるか</Description>
      <Maximum>1</Maximum>
      <SortID>95000</SortID>
    </Field>
    <Field Def="u8 modelDispMask0:1">
      <DisplayName>モデル表示マスク0</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84000</SortID>
    </Field>
    <Field Def="u8 modelDispMask1:1">
      <DisplayName>モデル表示マスク1</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84001</SortID>
    </Field>
    <Field Def="u8 modelDispMask2:1">
      <DisplayName>モデル表示マスク2</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84002</SortID>
    </Field>
    <Field Def="u8 modelDispMask3:1">
      <DisplayName>モデル表示マスク3</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84003</SortID>
    </Field>
    <Field Def="u8 modelDispMask4:1">
      <DisplayName>モデル表示マスク4</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84004</SortID>
    </Field>
    <Field Def="u8 modelDispMask5:1">
      <DisplayName>モデル表示マスク5</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84005</SortID>
    </Field>
    <Field Def="u8 modelDispMask6:1">
      <DisplayName>モデル表示マスク6</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84006</SortID>
    </Field>
    <Field Def="u8 modelDispMask7:1">
      <DisplayName>モデル表示マスク7</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84007</SortID>
    </Field>
    <Field Def="u8 modelDispMask8:1">
      <DisplayName>モデル表示マスク8</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84008</SortID>
    </Field>
    <Field Def="u8 modelDispMask9:1">
      <DisplayName>モデル表示マスク9</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84009</SortID>
    </Field>
    <Field Def="u8 modelDispMask10:1">
      <DisplayName>モデル表示マスク10</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84010</SortID>
    </Field>
    <Field Def="u8 modelDispMask11:1">
      <DisplayName>モデル表示マスク11</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84011</SortID>
    </Field>
    <Field Def="u8 modelDispMask12:1">
      <DisplayName>モデル表示マスク12</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84012</SortID>
    </Field>
    <Field Def="u8 modelDispMask13:1">
      <DisplayName>モデル表示マスク13</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84013</SortID>
    </Field>
    <Field Def="u8 modelDispMask14:1">
      <DisplayName>モデル表示マスク14</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84014</SortID>
    </Field>
    <Field Def="u8 modelDispMask15:1">
      <DisplayName>モデル表示マスク15</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84015</SortID>
    </Field>
    <Field Def="u8 isEnableNeckTurn:1">
      <DisplayName>首振り有効にするか</DisplayName>
      <Description>パラムウィーバで設定された首振りを有効にするか。</Description>
      <Maximum>1</Maximum>
      <SortID>123000</SortID>
    </Field>
    <Field Def="u8 disableRespawn:1">
      <DisplayName>リスポン禁止か</DisplayName>
      <Description>リスポンを禁止するか</Description>
      <Maximum>1</Maximum>
      <SortID>124000</SortID>
    </Field>
    <Field Def="u8 isMoveAnimWait:1">
      <DisplayName>移動アニメを待つか</DisplayName>
      <Description>移動アニメをアニメが終わるまで再生するか。（カゲロウ龍の様に。）</Description>
      <Maximum>1</Maximum>
      <SortID>7500</SortID>
    </Field>
    <Field Def="u8 isCrowd:1">
      <DisplayName>群集用処理軽減するか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>群集時の処理負荷軽減を行なうか。赤子用（できればファランクスも）</Description>
      <Maximum>1</Maximum>
      <SortID>96000</SortID>
    </Field>
    <Field Def="u8 isWeakB:1">
      <DisplayName>特攻Bか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>特攻Bか。特攻Bダメージ倍率が計算に含まれるようになります</Description>
      <Maximum>1</Maximum>
      <SortID>11001</SortID>
    </Field>
    <Field Def="u8 isWeakC:1">
      <DisplayName>特攻Cか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>特攻Cか。特攻Cダメージ倍率が計算に含まれるようになります</Description>
      <Maximum>1</Maximum>
      <SortID>11002</SortID>
    </Field>
    <Field Def="u8 isWeakD:1">
      <DisplayName>特攻Dか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>特攻Dか。特攻Dダメージ倍率が計算に含まれるようになります</Description>
      <Maximum>1</Maximum>
      <SortID>11003</SortID>
    </Field>
    <Field Def="u8 doesAlwaysUseSpecialTurn:1">
      <DisplayName>常時特殊旋回するか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>常時特殊旋回を実行するか(旋回移動先にナビメッシュがない場合も特殊旋回を継続実行します)</Description>
      <Maximum>1</Maximum>
      <SortID>201002</SortID>
    </Field>
    <Field Def="u8 isRideAtkTarget:1">
      <DisplayName>騎乗特攻か</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>（騎乗中であれば）騎乗特攻の対象になるか</Description>
      <Maximum>1</Maximum>
      <SortID>11006</SortID>
    </Field>
    <Field Def="u8 isEnableStepDispInterpolate:1">
      <DisplayName>段差越え表示補間を使用するか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>段差越え表示補間を使用するか</Description>
      <Maximum>1</Maximum>
      <SortID>200500</SortID>
    </Field>
    <Field Def="u8 isStealthTarget:1 = 1">
      <DisplayName>ステルス攻撃対象か</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ステルス攻撃対象か</Description>
      <Maximum>1</Maximum>
      <SortID>11008</SortID>
    </Field>
    <Field Def="u8 disableInitializeDead:1">
      <DisplayName>初期死亡しない</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>初期死亡をしない場合にTRUE、殺してセーブしても死体再現されません。</Description>
      <Maximum>1</Maximum>
      <SortID>124001</SortID>
    </Field>
    <Field Def="u8 isHitRumble:1">
      <DisplayName>ヒット時振動するか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ヒット時振動をする場合TRUE。亡者など、普通のヒットストップと変えたいときにつかう。</Description>
      <Maximum>1</Maximum>
      <SortID>9520</SortID>
    </Field>
    <Field Def="u8 isSmoothTurn:1 = 1">
      <DisplayName>スムーズ旋回するか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ルート移動でのノード間旋回時、補間を行うか否か</Description>
      <Maximum>1</Maximum>
      <SortID>202000</SortID>
    </Field>
    <Field Def="u8 isWeakE:1">
      <DisplayName>特攻Eか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>特攻Eか。特攻Eダメージ倍率が計算に含まれるようになります</Description>
      <Maximum>1</Maximum>
      <SortID>11004</SortID>
    </Field>
    <Field Def="u8 isWeakF:1">
      <DisplayName>特攻Fか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>特攻Fか。特攻Fダメージ倍率が計算に含まれるようになります</Description>
      <Maximum>1</Maximum>
      <SortID>11005</SortID>
    </Field>
    <Field Def="u8 modelDispMask16:1">
      <DisplayName>モデル表示マスク16</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84016</SortID>
    </Field>
    <Field Def="u8 modelDispMask17:1">
      <DisplayName>モデル表示マスク17</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84017</SortID>
    </Field>
    <Field Def="u8 modelDispMask18:1">
      <DisplayName>モデル表示マスク18</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84018</SortID>
    </Field>
    <Field Def="u8 modelDispMask19:1">
      <DisplayName>モデル表示マスク19</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84019</SortID>
    </Field>
    <Field Def="u8 modelDispMask20:1">
      <DisplayName>モデル表示マスク20</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84020</SortID>
    </Field>
    <Field Def="u8 modelDispMask21:1">
      <DisplayName>モデル表示マスク21</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84021</SortID>
    </Field>
    <Field Def="u8 modelDispMask22:1">
      <DisplayName>モデル表示マスク22</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84022</SortID>
    </Field>
    <Field Def="u8 modelDispMask23:1">
      <DisplayName>モデル表示マスク23</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84023</SortID>
    </Field>
    <Field Def="u8 modelDispMask24:1">
      <DisplayName>モデル表示マスク24</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84024</SortID>
    </Field>
    <Field Def="u8 modelDispMask25:1">
      <DisplayName>モデル表示マスク25</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84025</SortID>
    </Field>
    <Field Def="u8 modelDispMask26:1">
      <DisplayName>モデル表示マスク26</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84026</SortID>
    </Field>
    <Field Def="u8 modelDispMask27:1">
      <DisplayName>モデル表示マスク27</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84027</SortID>
    </Field>
    <Field Def="u8 modelDispMask28:1">
      <DisplayName>モデル表示マスク28</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84028</SortID>
    </Field>
    <Field Def="u8 modelDispMask29:1">
      <DisplayName>モデル表示マスク29</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84029</SortID>
    </Field>
    <Field Def="u8 modelDispMask30:1">
      <DisplayName>モデル表示マスク30</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84030</SortID>
    </Field>
    <Field Def="u8 modelDispMask31:1">
      <DisplayName>モデル表示マスク31</DisplayName>
      <Description>表示マスクに対応するモデルを表示します。</Description>
      <Maximum>1</Maximum>
      <SortID>84031</SortID>
    </Field>
    <Field Def="f32 itemSearchRadius">
      <DisplayName>ドロップアイテム半径補正</DisplayName>
      <Description>通常のItem検索判定の円柱半径に、補正として足し合わせる半径(敵ドロップアイテムに適用。大きなキャラなどで使用する)</Description>
      <Minimum>-99</Minimum>
      <Maximum>99</Maximum>
      <Increment>0.1</Increment>
      <SortID>26001</SortID>
    </Field>
    <Field Def="f32 chrHitHeight">
      <DisplayName>対キャラあたりの高さ[m]</DisplayName>
      <Description>対キャラ当たりカプセルの高さ.</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>14000</SortID>
    </Field>
    <Field Def="f32 chrHitRadius">
      <DisplayName>対キャラあたりの半径[m]</DisplayName>
      <Description>対キャラ当たりカプセルの半径.</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>14100</SortID>
    </Field>
    <Field Def="u8 specialTurnType">
      <DisplayName>特殊旋回のタイプ</DisplayName>
      <Enum>NPC_SPECIAL_TURN_TYPE</Enum>
      <Description>特殊旋回のタイプ</Description>
      <Maximum>2</Maximum>
      <SortID>201000</SortID>
    </Field>
    <Field Def="u8 isSoulGetByBoss:1">
      <DisplayName>ソウルはボス入手か</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ソウルはボス入手か</Description>
      <Maximum>1</Maximum>
      <SortID>27010</SortID>
    </Field>
    <Field Def="u8 isBulletOwner_byObject:1">
      <DisplayName>オブジェクト扱いの弾丸オーナか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>弾丸のオーナーとなった場合、弾丸に関連するダメージ計算などをオブジェのものを適用するようにするフラグ。勢力別ダメージ補正で使用。</Description>
      <Maximum>1</Maximum>
      <SortID>410000</SortID>
    </Field>
    <Field Def="u8 isUseLowHitFootIk:1">
      <DisplayName>ロウヒットFootIKを使うか？</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ロウヒット用のFootIkフィルターを使用するか</Description>
      <Maximum>1</Maximum>
      <SortID>420000</SortID>
    </Field>
    <Field Def="u8 isCalculatePvPDamage:1">
      <DisplayName>PvPのダメージ補正制御を適用するか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ダメージ計算時に「プレイヤー」としてダメージ計算するのかを決める。無効の場合は「敵」扱い。</Description>
      <Maximum>1</Maximum>
      <SortID>430000</SortID>
    </Field>
    <Field Def="u8 isHostSyncChr:1">
      <DisplayName>ホスト世界でアクティブ時のみアクティベート可</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ホスト世界でアクティブ時のみアクティベート可</Description>
      <Maximum>1</Maximum>
      <SortID>450150</SortID>
    </Field>
    <Field Def="u8 isSkipWeakDamageAnim:1">
      <DisplayName>弱点アニメをスキップするか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>弱点ダメージアニメ再生をスキップするかどうか。アニメを再生しないだけで「部位ダメージ率」「防御材質」は弱点として扱われます。</Description>
      <Maximum>1</Maximum>
      <SortID>51105</SortID>
    </Field>
    <Field Def="u8 isKeepHitOnRide:1">
      <DisplayName>騎乗された時、乗る側のカプセルあたりを有効にするか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>このパラメータが○のキャラに騎乗する際、騎乗中、キャラのアタリが残ったままになる </Description>
      <Maximum>1</Maximum>
      <SortID>9505</SortID>
    </Field>
    <Field Def="u8 isSpCollide:1">
      <DisplayName>特殊あたりキャラか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>特殊あたりに当たるキャラか</Description>
      <Maximum>1</Maximum>
      <SortID>12960</SortID>
    </Field>
    <Field Def="u16 def_dark">
      <DisplayName>闇防御力</DisplayName>
      <Description>闇攻撃に対するダメージ減少基本値.</Description>
      <Maximum>9999</Maximum>
      <SortID>35700</SortID>
    </Field>
    <Field Def="u32 threatLv = 1">
      <DisplayName>脅威度</DisplayName>
      <Description>脅威度。0ならPCが見つかっても「見つかりそうFE」を表示しない</Description>
      <Maximum>9999</Maximum>
      <SortID>460000</SortID>
    </Field>
    <Field Def="f32 specialTurnDistanceThreshold = 4">
      <DisplayName>特殊旋回の使用距離の閾値[m]</DisplayName>
      <Description>ターゲットとの距離が設定された閾値以上の場合に、特殊旋回を行う</Description>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>201001</SortID>
    </Field>
    <Field Def="s32 autoFootEffectSfxId = -1">
      <DisplayName>フットエフェクト識別子</DisplayName>
      <Description>自動フットエフェクトで使用するSFX識別子。（XYYZZZのZZZ）</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>97100</SortID>
    </Field>
    <Field Def="u16 materialSe1">
      <DisplayName>防御材質1【SE】</DisplayName>
      <Enum>WEP_MATERIAL_DEF</Enum>
      <Description>ダメージを受けたときに鳴らすＳＥを判定する。1.見た目で設定してＯＫ.</Description>
      <Maximum>9999</Maximum>
      <SortID>17000</SortID>
    </Field>
    <Field Def="u16 materialSfx1">
      <DisplayName>防御材質1【SFX】</DisplayName>
      <Enum>WEP_MATERIAL_DEF_SFX</Enum>
      <Description>ダメージを受けたときに発生するSFXを判定する。1.見た目で設定してＯＫ.</Description>
      <Maximum>9999</Maximum>
      <SortID>18000</SortID>
    </Field>
    <Field Def="u16 materialSe_Weak2">
      <DisplayName>弱点防御材質2【SE】</DisplayName>
      <Enum>WEP_MATERIAL_DEF</Enum>
      <Description>弱点部位ダメージを受けた時に鳴らすSEを判定する。2</Description>
      <Maximum>9999</Maximum>
      <SortID>51201</SortID>
    </Field>
    <Field Def="u16 materialSfx_Weak2">
      <DisplayName>弱点防御材質2【SFX】</DisplayName>
      <Enum>WEP_MATERIAL_DEF_SFX</Enum>
      <Description>弱点部位ダメージを受けた時に発生するSFXを判定する。2</Description>
      <Maximum>9999</Maximum>
      <SortID>51301</SortID>
    </Field>
    <Field Def="u16 materialSe2">
      <DisplayName>防御材質2【SE】</DisplayName>
      <Enum>WEP_MATERIAL_DEF</Enum>
      <Description>ダメージを受けたときに鳴らすＳＥを判定する。2.見た目で設定してＯＫ.</Description>
      <Maximum>9999</Maximum>
      <SortID>17001</SortID>
    </Field>
    <Field Def="u16 materialSfx2">
      <DisplayName>防御材質2【SFX】</DisplayName>
      <Enum>WEP_MATERIAL_DEF_SFX</Enum>
      <Description>ダメージを受けたときに発生するSFXを判定する。2.見た目で設定してＯＫ.</Description>
      <Maximum>9999</Maximum>
      <SortID>18001</SortID>
    </Field>
    <Field Def="s32 spEffectID8 = -1">
      <DisplayName>常駐特殊効果8</DisplayName>
      <Description>常駐特殊効果8</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50040</SortID>
    </Field>
    <Field Def="s32 spEffectID9 = -1">
      <DisplayName>常駐特殊効果9</DisplayName>
      <Description>常駐特殊効果9</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50050</SortID>
    </Field>
    <Field Def="s32 spEffectID10 = -1">
      <DisplayName>常駐特殊効果10</DisplayName>
      <Description>常駐特殊効果10</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50060</SortID>
    </Field>
    <Field Def="s32 spEffectID11 = -1">
      <DisplayName>常駐特殊効果11</DisplayName>
      <Description>常駐特殊効果11</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50070</SortID>
    </Field>
    <Field Def="s32 spEffectID12 = -1">
      <DisplayName>常駐特殊効果12</DisplayName>
      <Description>常駐特殊効果12</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50080</SortID>
    </Field>
    <Field Def="s32 spEffectID13 = -1">
      <DisplayName>常駐特殊効果13</DisplayName>
      <Description>常駐特殊効果13</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50090</SortID>
    </Field>
    <Field Def="s32 spEffectID14 = -1">
      <DisplayName>常駐特殊効果14</DisplayName>
      <Description>常駐特殊効果14</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50100</SortID>
    </Field>
    <Field Def="s32 spEffectID15 = -1">
      <DisplayName>常駐特殊効果15</DisplayName>
      <Description>常駐特殊効果15</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>50110</SortID>
    </Field>
    <Field Def="s32 autoFootEffectDecalBaseId1 = -1">
      <DisplayName>フットデカール識別子1</DisplayName>
      <Description>フットエフェクト発生時に貼られるデカール。床材質も考慮される</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>97110</SortID>
    </Field>
    <Field Def="u32 toughness">
      <DisplayName>強靭度</DisplayName>
      <Description>強靭度の基本値</Description>
      <Maximum>999</Maximum>
      <SortID>39300</SortID>
    </Field>
    <Field Def="f32 toughnessRecoverCorrection">
      <DisplayName>強靭度 回復時間補正値</DisplayName>
      <Description>強靭度の回復時間用の補正値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>39400</SortID>
    </Field>
    <Field Def="f32 neutralDamageCutRate = 1">
      <DisplayName>無属性ダメージ倍率</DisplayName>
      <Description>無属性ダメージ倍率。ダメージ計算結果にこの値をかけた値が最終ダメージ値になります。</Description>
      <DisplayFormat>%.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>36000</SortID>
    </Field>
    <Field Def="f32 slashDamageCutRate = 1">
      <DisplayName>斬撃ダメージ倍率</DisplayName>
      <Description>斬撃ダメージ倍率。ダメージ計算結果にこの値をかけた値が最終ダメージ値になります。</Description>
      <DisplayFormat>%.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>36100</SortID>
    </Field>
    <Field Def="f32 blowDamageCutRate = 1">
      <DisplayName>打撃ダメージ倍率</DisplayName>
      <Description>打撃ダメージ倍率。ダメージ計算結果にこの値をかけた値が最終ダメージ値になります。</Description>
      <DisplayFormat>%.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>36200</SortID>
    </Field>
    <Field Def="f32 thrustDamageCutRate = 1">
      <DisplayName>刺突ダメージ倍率</DisplayName>
      <Description>刺突ダメージ倍率。ダメージ計算結果にこの値をかけた値が最終ダメージ値になります。</Description>
      <DisplayFormat>%.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>36300</SortID>
    </Field>
    <Field Def="f32 magicDamageCutRate = 1">
      <DisplayName>魔法ダメージ倍率</DisplayName>
      <Description>魔法ダメージ倍率。ダメージ計算結果にこの値をかけた値が最終ダメージ値になります。</Description>
      <DisplayFormat>%.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>36400</SortID>
    </Field>
    <Field Def="f32 fireDamageCutRate = 1">
      <DisplayName>火炎ダメージ倍率</DisplayName>
      <Description>火炎ダメージ倍率。ダメージ計算結果にこの値をかけた値が最終ダメージ値になります。</Description>
      <DisplayFormat>%.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>36500</SortID>
    </Field>
    <Field Def="f32 thunderDamageCutRate = 1">
      <DisplayName>電撃ダメージ倍率</DisplayName>
      <Description>電撃ダメージ倍率。ダメージ計算結果にこの値をかけた値が最終ダメージ値になります。</Description>
      <DisplayFormat>%.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>36600</SortID>
    </Field>
    <Field Def="f32 darkDamageCutRate = 1">
      <DisplayName>闇ダメージ倍率</DisplayName>
      <Description>闇ダメージ倍率。ダメージ計算結果にこの値をかけた値が最終ダメージ値になります。</Description>
      <DisplayFormat>%.3f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>99.999</Maximum>
      <Increment>0.001</Increment>
      <SortID>36700</SortID>
    </Field>
    <Field Def="f32 darkGuardCutRate">
      <DisplayName>闇攻撃力カット率[％]</DisplayName>
      <Description>闇攻撃をどれだけカットするか？</Description>
      <DisplayFormat>%0.1f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>61200</SortID>
    </Field>
    <Field Def="s8 clothUpdateOffset">
      <DisplayName>クロス更新優先度オフセット[m]</DisplayName>
      <Description>クロス更新優先度オフセット[m]</Description>
      <SortID>140000</SortID>
    </Field>
    <Field Def="u8 npcPlayerWeightType">
      <DisplayName>NPCプレイヤー時重量設定</DisplayName>
      <Enum>NPC_WEIGHT_TYPE</Enum>
      <Description>NPCプレイヤーのときに適用される装備重量タイプ</Description>
      <Maximum>5</Maximum>
      <SortID>7700</SortID>
    </Field>
    <Field Def="s16 normalChangeModelId = -1">
      <DisplayName>通常時差し替えモデルID</DisplayName>
      <Description>通常時の差し替えモデル、テクスチャID</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>99100</SortID>
    </Field>
    <Field Def="s16 normalChangeAnimChrId = -1">
      <DisplayName>通常時差し替えアニメキャラID</DisplayName>
      <Description>対象のアニメを指定IDのAnibndで差し替える</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>100200</SortID>
    </Field>
    <Field Def="u16 paintRenderTargetSize = 256">
      <DisplayName>ペイントレンダーターゲットサイズ[pix]</DisplayName>
      <Description>ペイントレンダーターゲットサイズ[pix]</Description>
      <EditFlags>None</EditFlags>
      <Maximum>4096</Maximum>
      <Increment>128</Increment>
      <SortID>200050</SortID>
    </Field>
    <Field Def="s32 resistCorrectId_disease = -1">
      <DisplayName>疫病耐性 補正ルールID</DisplayName>
      <Description>状態異常の発動時、状態異常耐性補正パラメータの設定値を使って、一時的に最大値を変動させる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>44100</SortID>
    </Field>
    <Field Def="s32 phantomShaderId = -1">
      <DisplayName>適用シェーダーID</DisplayName>
      <Description>適用するファントムパラメータ.xlsmのID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>4500</SortID>
    </Field>
    <Field Def="s32 multiPlayCorrectionParamId = -1">
      <DisplayName>マルチプレイ補正パラメータID</DisplayName>
      <Description>マルチプレイ補正パラメータID</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>400000</SortID>
    </Field>
    <Field Def="f32 maxAnklePitchAngle = -1">
      <DisplayName>FootIK足首の制限角度_ピッチ</DisplayName>
      <Description>FootIK足首のピッチの制限角度（-1：制限なし）。HATでFoot End L Sを設定していない場合はこの角度がロールと共通で使用される。</Description>
      <Minimum>-1</Minimum>
      <Maximum>90</Maximum>
      <Increment>0.1</Increment>
      <SortID>200060</SortID>
    </Field>
    <Field Def="u16 resist_freeze">
      <DisplayName>冷気耐性</DisplayName>
      <Description>冷気状態異常へのかかりにくさ</Description>
      <Maximum>999</Maximum>
      <SortID>43400</SortID>
    </Field>
    <Field Def="s8 freezeGuardResist">
      <DisplayName>冷気攻撃カット率[％]</DisplayName>
      <Description>冷気に対する攻撃力（特殊効果パラメータに設定）をどれだけカットするか</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>63400</SortID>
    </Field>
    
    <Field Def="dummy8 pad1[1]">
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>2000003</SortID>
    </Field>
    
    <Field Def="s32 lockCameraParamId = -1">
      <DisplayName>ロックカメラパラメータID</DisplayName>
      <Description>ロックオンされた際にカメラに適用させるロックカメラパラメータのID。最も優先度が高い。-1なら未使用</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>10500</SortID>
    </Field>
    <Field Def="s32 spEffectID16 = -1">
      <DisplayName>常駐特殊効果16</DisplayName>
      <Description>常駐特殊効果16</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50111</SortID>
    </Field>
    <Field Def="s32 spEffectID17 = -1">
      <DisplayName>常駐特殊効果17</DisplayName>
      <Description>常駐特殊効果17</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50112</SortID>
    </Field>
    <Field Def="s32 spEffectID18 = -1">
      <DisplayName>常駐特殊効果18</DisplayName>
      <Description>常駐特殊効果18</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50113</SortID>
    </Field>
    <Field Def="s32 spEffectID19 = -1">
      <DisplayName>常駐特殊効果19</DisplayName>
      <Description>常駐特殊効果19</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50114</SortID>
    </Field>
    <Field Def="s32 spEffectID20 = -1">
      <DisplayName>常駐特殊効果20</DisplayName>
      <Description>常駐特殊効果20</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50115</SortID>
    </Field>
    <Field Def="s32 spEffectID21 = -1">
      <DisplayName>常駐特殊効果21</DisplayName>
      <Description>常駐特殊効果21</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50116</SortID>
    </Field>
    <Field Def="s32 spEffectID22 = -1">
      <DisplayName>常駐特殊効果22</DisplayName>
      <Description>常駐特殊効果22</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50117</SortID>
    </Field>
    <Field Def="s32 spEffectID23 = -1">
      <DisplayName>常駐特殊効果23</DisplayName>
      <Description>常駐特殊効果23</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50118</SortID>
    </Field>
    <Field Def="s32 spEffectID24 = -1">
      <DisplayName>常駐特殊効果24</DisplayName>
      <Description>常駐特殊効果24</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50119</SortID>
    </Field>
    <Field Def="s32 spEffectID25 = -1">
      <DisplayName>常駐特殊効果25</DisplayName>
      <Description>常駐特殊効果25</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50120</SortID>
    </Field>
    <Field Def="s32 spEffectID26 = -1">
      <DisplayName>常駐特殊効果26</DisplayName>
      <Description>常駐特殊効果26</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50121</SortID>
    </Field>
    <Field Def="s32 spEffectID27 = -1">
      <DisplayName>常駐特殊効果27</DisplayName>
      <Description>常駐特殊効果27</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50122</SortID>
    </Field>
    <Field Def="s32 spEffectID28 = -1">
      <DisplayName>常駐特殊効果28</DisplayName>
      <Description>常駐特殊効果28</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50123</SortID>
    </Field>
    <Field Def="s32 spEffectID29 = -1">
      <DisplayName>常駐特殊効果29</DisplayName>
      <Description>常駐特殊効果29</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50124</SortID>
    </Field>
    <Field Def="s32 spEffectID30 = -1">
      <DisplayName>常駐特殊効果30</DisplayName>
      <Description>常駐特殊効果30</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50125</SortID>
    </Field>
    <Field Def="s32 spEffectID31 = -1">
      <DisplayName>常駐特殊効果31</DisplayName>
      <Description>常駐特殊効果31</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>50126</SortID>
    </Field>
    <Field Def="f32 disableLockOnAng">
      <DisplayName>ロック不可領域の中心角[deg]</DisplayName>
      <Description>敵の真下に円錐状のロックオン不可領域を作る。円錐の広さの角度。TAEから一時的に変更可能</Description>
      <Minimum>0</Minimum>
      <Maximum>360</Maximum>
      <Increment>1</Increment>
      <SortID>10800</SortID>
    </Field>
    <Field Def="s8 clothOffLodLevel = -1">
      <DisplayName>クロスOffLODレベル</DisplayName>
      <Description>クロスの処理を切るLODレベルを設定する</Description>
      <Minimum>-1</Minimum>
      <Maximum>2</Maximum>
      <SortID>141000</SortID>
    </Field>
    <Field Def="u8 isUseFootIKNormalByUnduration:1">
      <DisplayName>起伏にあわせるのにFootIK結果を用いるか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>キャラを地面の起伏に合わせる際に、FootIK結果を用いるか。飛行キャラの場合は使用不可</Description>
      <Maximum>1</Maximum>
      <SortID>19500</SortID>
    </Field>
    <Field Def="u8 attachHitInitializeDead:1">
      <DisplayName>初期死亡時にカプセル接地するか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>初期死亡時にカプセル接地するか</Description>
      <Maximum>1</Maximum>
      <SortID>124005</SortID>
    </Field>
    <Field Def="u8 excludeGroupRewardCheck:1">
      <DisplayName>グループ報酬判定から外すか</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>グループ報酬：「全員死亡」の判定において、このパラメータが○のキャラは判定から除外する </Description>
      <Maximum>1</Maximum>
      <SortID>760000</SortID>
    </Field>
    <Field Def="u8 enableAILockDmyPoly_212:1 = 1">
      <DisplayName>ロックダミポリ(エネミー用)212が有効か</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ロックダミポリ(エネミー用)212が有効か</Description>
      <Maximum>1</Maximum>
      <SortID>10900</SortID>
    </Field>
    <Field Def="u8 enableAILockDmyPoly_213:1 = 1">
      <DisplayName>ロックダミポリ(エネミー用)213が有効か</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ロックダミポリ(エネミー用)213が有効か</Description>
      <Maximum>1</Maximum>
      <SortID>10901</SortID>
    </Field>
    <Field Def="u8 enableAILockDmyPoly_214:1 = 1">
      <DisplayName>ロックダミポリ(エネミー用)214が有効か</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>ロックダミポリ(エネミー用)214が有効か</Description>
      <Maximum>1</Maximum>
      <SortID>10902</SortID>
    </Field>
    <Field Def="u8 disableActivateOpen_xb1:1">
      <DisplayName>オープン_XB1から除外</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>オープン_XB1から除外</Description>
      <Maximum>1</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="u8 disableActivateLegacy_xb1:1">
      <DisplayName>レガシー_XB1から除外</DisplayName>
      <Enum>NPC_BOOL</Enum>
      <Description>レガシー_XB1から除外</Description>
      <Maximum>1</Maximum>
      <SortID>110</SortID>
    </Field>
    <Field Def="s16 estusFlaskRecoveryParamId = -1">
      <DisplayName>HPエスト瓶／MPエスト瓶回復数パラメータID</DisplayName>
      <Description>キャラクター死亡時に値と同じ エスト使用回数回復パラメータ.xlsm　のデータIDを取得してエスト瓶を回復させる。 -1なら未使用</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>750000</SortID>
    </Field>
    <Field Def="s32 roleNameId = -1">
      <DisplayName>ロール名テキストID</DisplayName>
      <Description>召喚時のロール名を指定する。-1:対象霊体のデフォルトロール名を使用。0:表示なし。1以上:テキストＩＤとして利用。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>405000</SortID>
    </Field>
    <Field Def="u16 estusFlaskLotPoint">
      <DisplayName>HP&amp;MPエスト瓶回復 抽選確率</DisplayName>
      <Description>敵を倒した際のHP/MPエストの回復確率。10000 を分母とし、分子をNPCパラから取得する。 </Description>
      <Maximum>10000</Maximum>
      <SortID>751000</SortID>
    </Field>
    <Field Def="u16 hpEstusFlaskLotPoint">
      <DisplayName>HPエスト瓶回復 抽選確率</DisplayName>
      <Description>敵を倒した際のMPエストの回復確率。10000 を分母とし、分子をNPCパラから取得する。 </Description>
      <Maximum>10000</Maximum>
      <SortID>751100</SortID>
    </Field>
    <Field Def="u16 mpEstusFlaskLotPoint">
      <DisplayName>MPエスト瓶回復 抽選確率</DisplayName>
      <Description>敵を倒した際のMPエストの回復確率。10000 を分母とし、分子をNPCパラから取得する。 </Description>
      <Maximum>10000</Maximum>
      <SortID>751200</SortID>
    </Field>
    <Field Def="u16 estusFlaskRecovery_failedLotPointAdd">
      <DisplayName>HP&amp;MPエスト瓶回復 落選時 加算抽選確率</DisplayName>
      <Description>HP/MPエスト回復抽選に外れた際の次回確率上昇値。分子の加算値。</Description>
      <Maximum>10000</Maximum>
      <SortID>752000</SortID>
    </Field>
    <Field Def="u16 hpEstusFlaskRecovery_failedLotPointAdd">
      <DisplayName>HPエスト瓶回復 落選時 加算抽選確率</DisplayName>
      <Description>HPエスト回復抽選に外れた際の次回確率上昇値。分子の加算値。</Description>
      <Maximum>10000</Maximum>
      <SortID>752100</SortID>
    </Field>
    <Field Def="u16 mpEstusFlaskRecovery_failedLotPointAdd">
      <DisplayName>MPエスト瓶回復 落選時 加算抽選確率</DisplayName>
      <Description>MPエスト回復抽選に外れた際の次回確率上昇値。分子の加算値。</Description>
      <Maximum>10000</Maximum>
      <SortID>752200</SortID>
    </Field>
    <Field Def="s32 WanderGhostPhantomId = -1">
      <DisplayName>ファントムシェーダを使用して徘徊ゴーストになるか</DisplayName>
      <Description>ゲスト側でだけ指定されたIDのファントムシェーダIDを指定して幻影化</Description>
      <Minimum>-1</Minimum>
      <Maximum>1000000000</Maximum>
      <SortID>95500</SortID>
    </Field>
    <Field Def="f32 hearingHeadSize = -1">
      <DisplayName>聴覚用 頭のサイズ[m]</DisplayName>
      <Description>聴覚判定時のカプセルオフセットの代わりに、設定するオフセットサイズ。0以上が設定されている場合のみ、この値をオフセットとして使用。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>14200</SortID>
    </Field>
    <Field Def="s16 SoundBankId = -1">
      <DisplayName>サウンドバンクID</DisplayName>
      <Description>サウンドバンクIDが指定できます -1：キャラID(リソース名)のバンクを使用</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1000000</SortID>
    </Field>
    <Field Def="u8 forwardUndulationLimit">
      <DisplayName>起伏にあわせる最大前後角度</DisplayName>
      <Description>起伏に前後の角度を合わせる場合の上限角度。全長が長い場合には低めに設定したほうがよいです。</Description>
      <Maximum>180</Maximum>
      <SortID>20000</SortID>
    </Field>
    <Field Def="u8 sideUndulationLimit">
      <DisplayName>起伏にあわせる最大左右角度</DisplayName>
      <Description>起伏に左右の角度を合わせる場合の上限角度。全長が長い場合には低めに設定したほうがよいです。</Description>
      <Maximum>180</Maximum>
      <SortID>20010</SortID>
    </Field>
    <Field Def="f32 deactiveMoveSpeed">
      <DisplayName>小隊ディアクティブ移動の移動速度[m/s]</DisplayName>
      <Description>小隊ディアクティブ移動の移動速度[m/s]</Description>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.1</Increment>
      <SortID>450200</SortID>
    </Field>
    <Field Def="f32 deactiveMoveDist">
      <DisplayName>小隊ディアクティブ移動に切り替わる距離[m]</DisplayName>
      <Description>小隊ディアクティブ移動に切り替わる距離[m]</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>1</Increment>
      <SortID>450250</SortID>
    </Field>
    <Field Def="f32 enableSoundObjDist = 48">
      <DisplayName>サウンド音源有効距離[m]</DisplayName>
      <Description>キャラ音源が有効なプレイヤーからの距離です。-1：全距離で有効</Description>
      <Minimum>-1</Minimum>
      <Maximum>4096</Maximum>
      <Increment>0.1</Increment>
      <SortID>1000002</SortID>
    </Field>
    <Field Def="f32 undulationCorrectGain = 0.1">
      <DisplayName>起伏にあわせる補正ゲイン値</DisplayName>
      <Description>起伏に角度を合わせる際の速度を設定する</Description>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>20100</SortID>
    </Field>
    <Field Def="s16 autoFootEffectDecalBaseId2 = -1">
      <DisplayName>フットデカール識別子2</DisplayName>
      <Description>フットエフェクト発生時に貼られるデカール。床材質も考慮される</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>97111</SortID>
    </Field>
    <Field Def="s16 autoFootEffectDecalBaseId3 = -1">
      <DisplayName>フットデカール識別子3</DisplayName>
      <Description>フットエフェクト発生時に貼られるデカール。床材質も考慮される</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>97112</SortID>
    </Field>
    <Field Def="s16 RetargetReferenceChrId = -1">
      <DisplayName>リターゲット参照キャラID</DisplayName>
      <Description>モーションのリターゲット先の指定の際に参照するキャラID</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>100300</SortID>
    </Field>
    <Field Def="s16 SfxResBankId = -1">
      <DisplayName>SFXリソースバンクID</DisplayName>
      <Description>SFXリソースバンクIDが指定できます -1：キャラID(リソース名)のバンクを使用</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1000100</SortID>
    </Field>
    <Field Def="f32 updateActivatePriolity = 1">
      <DisplayName>更新とアクティベイトの優先度</DisplayName>
      <Description>アクティベート・更新レベルの決定に使用する。大きいほどプレイヤーから離れていても更新レベルが下がらない。</Description>
      <Minimum>0</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>450050</SortID>
    </Field>
    <Field Def="u8 chrNavimeshFlag_Alive">
      <DisplayName>死亡前ナビメッシュフラグ</DisplayName>
      <Enum>NPC_NAVIMESH_FLAG</Enum>
      <Description>キャラクターが生存してる間、触れているナビメッシュに値のフラグを設定する。移動に追従しない。</Description>
      <Maximum>3</Maximum>
      <SortID>470000</SortID>
    </Field>
    <Field Def="u8 chrNavimeshFlag_Dead">
      <DisplayName>死亡後ナビメッシュフラグ</DisplayName>
      <Enum>NPC_NAVIMESH_FLAG</Enum>
      <Description>キャラクターが死亡してる間、触れているナビメッシュに値のフラグを設定する。移動に追従しない。</Description>
      <Maximum>3</Maximum>
      <SortID>470010</SortID>
    </Field>
    
    <Field Def="dummy8 pad7[1]" >
      <DisplayName>pad</DisplayName>
      <DisplayFormat>%d</DisplayFormat>
      <EditFlags>Wrap</EditFlags>
      <SortID>2000004</SortID>
    </Field>
    
    <Field Def="u8 wheelRotType">
      <DisplayName>車輪制御タイプ</DisplayName>
      <Enum>NPC_WHEEL_ROT_TYPE</Enum>
      <Description>車輪制御タイプ</Description>
      <Maximum>3</Maximum>
      <SortID>16300</SortID>
    </Field>
    <Field Def="f32 wheelRotRadius">
      <DisplayName>車輪の半径</DisplayName>
      <Description>車輪の半径を指定[m]</Description>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>16350</SortID>
    </Field>
    <Field Def="f32 retargetMoveRate = 1">
      <DisplayName>リターゲット移動量倍率</DisplayName>
      <Description>リターゲット時の移動量の倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>100400</SortID>
    </Field>
    <Field Def="f32 ladderWarpOffset">
      <DisplayName>はしごワープ位置オフセット</DisplayName>
      <Description>指定された値でダミポリZ軸方向にオフセットします。正数・負数どちらも指定可能です。</Description>
      <Minimum>-99.99</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>16400</SortID>
    </Field>
    <Field Def="s32 loadAssetId = -1">
      <DisplayName>読み込みアセットID</DisplayName>
      <Description>キャラロード時に関連して読み込むアセットID（キャラが動的に生成するなど用。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>100250</SortID>
    </Field>
    <Field Def="s32 overlapCameraDmypolyId = -1">
      <DisplayName>オーバーラップカメラ対象ロックダミポリID</DisplayName>
      <Description>オーバーラップカメラを有効にするダミポリID(220～227)を設定します。-1の場合は無効になります。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>10700</SortID>
    </Field>
    <Field Def="s32 residentMaterialExParamId00 = -1">
      <DisplayName>常駐マテリアル拡張パラID0</DisplayName>
      <Description>常駐マテリアル拡張パラID0</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>90000</SortID>
    </Field>
    <Field Def="s32 residentMaterialExParamId01 = -1">
      <DisplayName>常駐マテリアル拡張パラID1</DisplayName>
      <Description>常駐マテリアル拡張パラID1</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>90010</SortID>
    </Field>
    <Field Def="s32 residentMaterialExParamId02 = -1">
      <DisplayName>常駐マテリアル拡張パラID2</DisplayName>
      <Description>常駐マテリアル拡張パラID2</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>90020</SortID>
    </Field>
    <Field Def="s32 residentMaterialExParamId03 = -1">
      <DisplayName>常駐マテリアル拡張パラID3</DisplayName>
      <Description>常駐マテリアル拡張パラID3</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>90030</SortID>
    </Field>
    <Field Def="s32 residentMaterialExParamId04 = -1">
      <DisplayName>常駐マテリアル拡張パラID4</DisplayName>
      <Description>常駐マテリアル拡張パラID4</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>90040</SortID>
    </Field>
    <Field Def="s32 sleepCollectorItemLotId_enemy = -1">
      <DisplayName>ネムリ時アイテム抽選ID_エネミー用</DisplayName>
      <Description>ネムリ収集時に取得するアイテムの抽選ID_エネミー用を指定。どちらか片方のみ設定してください。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>29000</SortID>
    </Field>
    <Field Def="s32 sleepCollectorItemLotId_map = -1">
      <DisplayName>ネムリ時アイテム抽選ID_マップ用</DisplayName>
      <Description>ネムリ収集時に取得するアイテムの抽選ID_マップ用を指定。どちらか片方のみ設定してください。</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>29001</SortID>
    </Field>
    <Field Def="f32 footIkErrorOnGain = 0.1">
      <DisplayName>FootIK見た目の高さ補正ONゲイン値</DisplayName>
      <Description>FootIK見た目の高さ補正ONゲイン値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>200200</SortID>
    </Field>
    <Field Def="f32 footIkErrorOffGain = 0.4">
      <DisplayName>FootIK見た目の高さ補正OFFゲイン値</DisplayName>
      <Description>FootIK見た目の高さ補正OFFゲイン値</Description>
      <DisplayFormat>%.2f</DisplayFormat>
      <Minimum>0</Minimum>
      <Maximum>1</Maximum>
      <SortID>200250</SortID>
    </Field>
    <Field Def="s16 SoundAddBankId = -1">
      <DisplayName>追加サウンドバンクID</DisplayName>
      <Description>追加のサウンドバンクIDが指定できます -1 or 0：追加なし(SEQ16135)</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <SortID>1000001</SortID>
    </Field>
    <Field Def="u8 materialVariationValue">
      <DisplayName>防御材質バリエーション値</DisplayName>
      <Description>防御材質と組み合わせて状態異常、ダメージSFX,SEのバリエーション分けに使用する値です。SEQ16473</Description>
      <Maximum>99</Maximum>
      <SortID>18002</SortID>
    </Field>
    <Field Def="u8 materialVariationValue_Weak">
      <DisplayName>弱点防御材質バリエーション値</DisplayName>
      <Description>弱点防御材質と組み合わせて状態異常、ダメージSFX,SEのバリエーション分けに使用する値です。SEQ16473</Description>
      <Maximum>99</Maximum>
      <SortID>51302</SortID>
    </Field>
    <Field Def="f32 superArmorDurability">
      <DisplayName>SA耐久力</DisplayName>
      <Description>スーパーアーマー耐久値</Description>
      <Minimum>-1</Minimum>
      <Maximum>9999</Maximum>
      <Increment>0.1</Increment>
      <SortID>39120</SortID>
    </Field>
    <Field Def="f32 saRecoveryRate = 1">
      <DisplayName>SA回復速度補正値</DisplayName>
      <Description>SA基礎回復量に乗算してSA回復速度を補正する</Description>
      <Minimum>-99.99</Minimum>
      <Maximum>99.99</Maximum>
      <Increment>0.1</Increment>
      <SortID>39250</SortID>
    </Field>
    <Field Def="f32 saGuardCutRate">
      <DisplayName>SA攻撃カット率[％]</DisplayName>
      <Description>ガード成功時のSＡダメージのカット率</Description>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <Increment>0.1</Increment>
      <SortID>67000</SortID>
    </Field>
    <Field Def="s32 resistCorrectId_blood = -1">
      <DisplayName>出血耐性 補正ルールID</DisplayName>
      <Description>状態異常の発動時、状態異常耐性補正パラメータの設定値を使って、一時的に最大値を変動させる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>44200</SortID>
    </Field>
    <Field Def="s32 resistCorrectId_curse = -1">
      <DisplayName>呪耐性 補正ルールID</DisplayName>
      <Description>状態異常の発動時、状態異常耐性補正パラメータの設定値を使って、一時的に最大値を変動させる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>44300</SortID>
    </Field>
    <Field Def="s32 resistCorrectId_freeze = -1">
      <DisplayName>冷気耐性 補正ルールID</DisplayName>
      <Description>状態異常の発動時、状態異常耐性補正パラメータの設定値を使って、一時的に最大値を変動させる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>44400</SortID>
    </Field>
    <Field Def="s32 resistCorrectId_sleep = -1">
      <DisplayName>睡眠耐性 補正ルールID</DisplayName>
      <Description>状態異常の発動時、状態異常耐性補正パラメータの設定値を使って、一時的に最大値を変動させる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>44500</SortID>
    </Field>
    <Field Def="s32 resistCorrectId_madness = -1">
      <DisplayName>発狂耐性 補正ルールID</DisplayName>
      <Description>状態異常の発動時、状態異常耐性補正パラメータの設定値を使って、一時的に最大値を変動させる</Description>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>44600</SortID>
    </Field>
    <Field Def="u32 chrDeadTutorialFlagId">
      <DisplayName>キャラ死亡チュートリアル判定フラグID</DisplayName>
      <Description>初めてキャラ倒した時のチュートリアル用のイベントフラグID。キャラ死亡時にフラグON。</Description>
      <DisplayFormat>%u</DisplayFormat>
      <Maximum>-294967297</Maximum>
      <SortID>761000</SortID>
    </Field>
    <Field Def="f32 stepDispInterpolateTime = 0.5">
      <DisplayName>段差越え表示補間時間</DisplayName>
      <Description>段差越え表示補間時間</Description>
      <Minimum>0.01</Minimum>
      <Maximum>999.9</Maximum>
      <SortID>200510</SortID>
    </Field>
    <Field Def="f32 stepDispInterpolateTriggerValue = 0.6">
      <DisplayName>段差越え表示起動判定値</DisplayName>
      <Description>段差越え表示起動判定値</Description>
      <Minimum>0.01</Minimum>
      <Maximum>999.9</Maximum>
      <SortID>200520</SortID>
    </Field>
    <Field Def="f32 lockScoreOffset">
      <DisplayName>ロックスコア補正値</DisplayName>
      <Description>ロックスコア補正値</Description>
      <Minimum>-999.9</Minimum>
      <Maximum>999.9</Maximum>
      <Increment>0.1</Increment>
      <SortID>461000</SortID>
    </Field>
    
    <Field Def="s32 newItemLotEnemyId" />
    <Field Def="s32 unknown_2" />
    <Field Def="f32 unknown_3" />
    <Field Def="f32 unknown_4" />
    <Field Def="f32 unknown_5" />
    <Field Def="f32 unknown_6" />
    <Field Def="f32 unknown_7" />
    <Field Def="f32 unknown_8" />
    <Field Def="f32 unknown_9" />
    <Field Def="f32 unknown_10" />
    <Field Def="f32 unknown_11" />
    <Field Def="f32 unknown_12" />
    <Field Def="f32 unknown_13" />
    <Field Def="f32 unknown_14" />
    <Field Def="f32 unknown_15" />
    <Field Def="f32 unknown_16" />
    <Field Def="f32 unknown_17" />
    <Field Def="f32 unknown_18" />
    <Field Def="f32 unknown_19" />
    <Field Def="f32 unknown_20" />
    <Field Def="s8 unknown_21a" />
    <Field Def="s8 unknown_21b" />
    <Field Def="u8 unknown_21c" />
    <Field Def="u8 unknown_21d" />
    <Field Def="f32 unknown_22" />
  </Fields>
</PARAMDEF>