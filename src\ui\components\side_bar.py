#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
侧边栏 - 导航菜单组件
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QPushButton, QButtonGroup,
                               QSpacerItem, QSizePolicy, QLabel)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont


class SideBar(QWidget):
    """侧边栏导航组件"""
    
    # 页面切换信号
    page_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self._init_ui()
        
    def _init_ui(self):
        """初始化用户界面"""
        self.setFixedWidth(250)
        
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(5)
        
        # 应用标题
        title_label = QLabel("ME3 Mod Manager")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #0078d4; margin: 10px 0 20px 0;")
        layout.addWidget(title_label)
        
        # 按钮组 - 确保只有一个按钮被选中
        self.button_group = QButtonGroup()
        self.button_group.setExclusive(True)
        
        # 创建导航按钮
        self.buttons = {}
        
        # 仪表板
        dashboard_btn = self._create_nav_button("📊 仪表板", "dashboard")
        dashboard_btn.setChecked(True)  # 默认选中
        layout.addWidget(dashboard_btn)
        
        # 基础配置
        config_btn = self._create_nav_button("⚙️ 基础配置", "basic_config")
        layout.addWidget(config_btn)
        
        # Mod配置
        mod_btn = self._create_nav_button("🎮 Mod配置", "mod_config")
        layout.addWidget(mod_btn)
        
        # 分隔符
        layout.addItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
        # 设置
        settings_btn = self._create_nav_button("🔧 设置", "settings")
        layout.addWidget(settings_btn)
        
        # 底部空间
        layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))
        
    def _create_nav_button(self, text: str, page_name: str) -> QPushButton:
        """创建导航按钮"""
        button = QPushButton(text)
        button.setObjectName("sideBarButton")
        button.setProperty("class", "sideBarButton")
        button.setCheckable(True)
        button.setFixedHeight(50)
        
        # 设置字体
        font = QFont()
        font.setPointSize(11)
        button.setFont(font)
        
        # 连接信号
        button.clicked.connect(lambda: self._on_button_clicked(page_name))
        
        # 添加到按钮组
        self.button_group.addButton(button)
        
        # 保存按钮引用
        self.buttons[page_name] = button
        
        return button
        
    def _on_button_clicked(self, page_name: str):
        """按钮点击处理"""
        self.page_changed.emit(page_name)
        
    def set_current_page(self, page_name: str):
        """设置当前页面"""
        if page_name in self.buttons:
            self.buttons[page_name].setChecked(True)
