<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>MULTI_SOUL_BONUS_RATE_PARAM_ST</ParamType>
  <DataVersion>3</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="f32 host">
      <DisplayName>ホスト</DisplayName>
      <Description>ホストの報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>1000</SortID>
    </Field>
    <Field Def="f32 WhiteGhost_None">
      <DisplayName>白サイン</DisplayName>
      <Description>協力サインの白霊の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>2000</SortID>
    </Field>
    <Field Def="f32 WhiteGhost_Umbasa">
      <DisplayName>金霊（太陽）</DisplayName>
      <Description>協力サインの金霊の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>2100</SortID>
    </Field>
    <Field Def="f32 WhiteGhost_Berserker">
      <DisplayName>白バーサーカー</DisplayName>
      <Description>協力サインの白バーサーカーの報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>2200</SortID>
    </Field>
    <Field Def="f32 BlackGhost_None_Sign">
      <DisplayName>赤サイン</DisplayName>
      <Description>敵対サインの赤霊の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>3000</SortID>
    </Field>
    <Field Def="f32 BlackGhost_Umbasa_Sign">
      <DisplayName>赤金霊（サイン）</DisplayName>
      <Description>敵対サインの赤金霊の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>3100</SortID>
    </Field>
    <Field Def="f32 BlackGhost_Berserker_Sign">
      <DisplayName>赤バーサーカー（サイン）</DisplayName>
      <Description>敵対サインの赤バーサーカーの報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>3200</SortID>
    </Field>
    <Field Def="f32 BlackGhost_None_Invade">
      <DisplayName>侵入</DisplayName>
      <Description>侵入の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>4000</SortID>
    </Field>
    <Field Def="f32 BlackGhost_Umbasa_Invade">
      <DisplayName>赤金霊（侵入）</DisplayName>
      <Description>侵入オーブの赤金霊の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>4500</SortID>
    </Field>
    <Field Def="f32 BlackGhost_Berserker_Invade">
      <DisplayName>赤バーサーカー（侵入）</DisplayName>
      <Description>侵入オーブの赤バーサーカーの報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>4600</SortID>
    </Field>
    <Field Def="f32 RedHunter1">
      <DisplayName>救援ゲスト</DisplayName>
      <Description>救援ゲストの報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>5000</SortID>
    </Field>
    <Field Def="f32 RedHunter2">
      <DisplayName>赤狩り霊２</DisplayName>
      <Description>赤狩り霊２の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>5100</SortID>
    </Field>
    <Field Def="f32 GuardianOfForest">
      <DisplayName>マップ守護霊(森)</DisplayName>
      <Description>マップ守護霊（森）の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>6000</SortID>
    </Field>
    <Field Def="f32 GuardianOfAnor">
      <DisplayName>マップ守護霊(アノール)</DisplayName>
      <Description>マップ守護霊(アノール)の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>6100</SortID>
    </Field>
    <Field Def="f32 BattleRoyal">
      <DisplayName>闘技場</DisplayName>
      <Description>闘技場の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>7000</SortID>
    </Field>
    <Field Def="f32 YellowMonk">
      <DisplayName>黄衣の翁</DisplayName>
      <Description>黄衣の翁の報酬ソウル倍率</Description>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>7100</SortID>
    </Field>
    <Field Def="dummy8 pad1[64]">
      <DisplayName>pad</DisplayName>
      <SortID>7101</SortID>
    </Field>
  </Fields>
</PARAMDEF>