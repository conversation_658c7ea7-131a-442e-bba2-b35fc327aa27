#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义标题栏 - 无边框窗口的标题栏组件
"""

from PySide6.QtWidgets import (QWidget, QHBoxLayout, QLabel, QPushButton, 
                               QSpacerItem, QSizePolicy)
from PySide6.QtCore import Qt, Signal, QPoint
from PySide6.QtGui import QFont, QMouseEvent


class CustomTitleBar(QWidget):
    """自定义标题栏"""
    
    # 信号定义
    minimize_clicked = Signal()
    maximize_clicked = Signal()
    close_clicked = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.drag_position = QPoint()
        
        self._init_ui()
        self._setup_style()
        
    def _init_ui(self):
        """初始化用户界面"""
        self.setObjectName("titleBar")
        self.setFixedHeight(40)
        
        # 主布局
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 应用图标和标题
        self.title_label = QLabel("ME3 Mod Manager")
        self.title_label.setObjectName("titleLabel")
        font = QFont()
        font.setPointSize(10)
        font.setBold(True)
        self.title_label.setFont(font)
        
        layout.addWidget(self.title_label)
        
        # 弹性空间
        spacer = QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        layout.addItem(spacer)
        
        # 窗口控制按钮容器
        buttons_widget = QWidget()
        buttons_widget.setObjectName("titleButtons")
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(0)
        
        # 最小化按钮
        self.minimize_btn = QPushButton("−")
        self.minimize_btn.setObjectName("minimizeBtn")
        self.minimize_btn.setToolTip("最小化")
        self.minimize_btn.clicked.connect(self.minimize_clicked.emit)
        
        # 最大化/还原按钮
        self.maximize_btn = QPushButton("□")
        self.maximize_btn.setObjectName("maximizeBtn")
        self.maximize_btn.setToolTip("最大化")
        self.maximize_btn.clicked.connect(self.maximize_clicked.emit)
        
        # 关闭按钮
        self.close_btn = QPushButton("×")
        self.close_btn.setObjectName("closeBtn")
        self.close_btn.setToolTip("关闭")
        self.close_btn.clicked.connect(self.close_clicked.emit)
        
        # 添加按钮到布局
        buttons_layout.addWidget(self.minimize_btn)
        buttons_layout.addWidget(self.maximize_btn)
        buttons_layout.addWidget(self.close_btn)
        
        layout.addWidget(buttons_widget)
        
    def _setup_style(self):
        """设置样式"""
        # 按钮字体
        button_font = QFont()
        button_font.setPointSize(12)
        button_font.setBold(True)
        
        for btn in [self.minimize_btn, self.maximize_btn, self.close_btn]:
            btn.setFont(button_font)
            btn.setFixedSize(45, 40)
            
    def update_maximize_button(self, is_maximized: bool):
        """更新最大化按钮状态"""
        if is_maximized:
            self.maximize_btn.setText("❐")
            self.maximize_btn.setToolTip("还原")
        else:
            self.maximize_btn.setText("□")
            self.maximize_btn.setToolTip("最大化")
            
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件 - 开始拖动"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            event.accept()
            
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件 - 拖动窗口"""
        if event.buttons() == Qt.LeftButton and not self.drag_position.isNull():
            if self.parent_window.isMaximized():
                # 如果窗口是最大化状态，先还原再拖动
                self.parent_window.showNormal()
                # 重新计算拖动位置
                self.drag_position = QPoint(self.parent_window.width() // 2, 20)
                
            self.parent_window.move(event.globalPosition().toPoint() - self.drag_position)
            event.accept()
            
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        self.drag_position = QPoint()
        event.accept()
        
    def mouseDoubleClickEvent(self, event: QMouseEvent):
        """双击事件 - 最大化/还原窗口"""
        if event.button() == Qt.LeftButton:
            self.maximize_clicked.emit()
            event.accept()
