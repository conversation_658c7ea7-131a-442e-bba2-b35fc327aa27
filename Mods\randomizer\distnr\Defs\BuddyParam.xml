<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>BUDDY_PARAM_ST</ParamType>
  <DataVersion>2</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="u8 disableParam_NT:1">
      <DisplayName>NT版出力から外すか</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>○をつけたパラメータをNT版パッケージでは除外します</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
    </Field>
    <Field Def="dummy8 disableParamReserve1:7">
      <DisplayName>パッケージ出力用リザーブ1</DisplayName>
      <Description>パッケージ出力用リザーブ1</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>1111</SortID>
    </Field>
    <Field Def="dummy8 disableParamReserve2[3]">
      <DisplayName>パッケージ出力用リザーブ2</DisplayName>
      <Description>パッケージ出力用リザーブ2</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>1112</SortID>
    </Field>
    <Field Def="s32 triggerSpEffectId = -1">
      <DisplayName>召喚特殊効果ID</DisplayName>
      <Description>召喚条件になる特殊効果IDを設定します </Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
    </Field>
    <Field Def="s32 npcParamId = -1">
      <DisplayName>NPCパラメータID</DisplayName>
      <Description>召喚されるバディのNPCパラメータIDを設定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="s32 npcThinkParamId = -1">
      <DisplayName>思考パラメータID</DisplayName>
      <Description>召喚されるバディのNPC思考パラメータIDを設定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="s32 npcParamId_ridden = -1">
      <DisplayName>騎乗（乗られる側）：NPCパラメータID</DisplayName>
      <Description>騎乗状態で召喚したいバディの場合、「乗られる側」のNPCパラメータIDを設定します </Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>300</SortID>
    </Field>
    <Field Def="s32 npcThinkParamId_ridden = -1">
      <DisplayName>騎乗（乗られる側）：思考パラメータID</DisplayName>
      <Description>騎乗状態で召喚したいバディの場合、「乗られる側」のNPC思考パラメータIDを設定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="f32 x_offset">
      <DisplayName>X：配置座標オフセット[m]</DisplayName>
      <Description>バディを召喚ポイントから、X座標にオフセットする距離をメートル単位で設定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-99.99</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>500</SortID>
    </Field>
    <Field Def="f32 z_offset">
      <DisplayName>Z：配置座標オフセット[m]</DisplayName>
      <Description>バディを召喚ポイントから、Z座標にオフセットする距離をメートル単位で設定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-99.99</Minimum>
      <Maximum>99.99</Maximum>
      <SortID>600</SortID>
    </Field>
    <Field Def="f32 y_angle">
      <DisplayName>Y：自分の配置角度[deg]</DisplayName>
      <Description>Y軸を中心に、自分を回転させる角度を設定します</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-180</Minimum>
      <Maximum>180</Maximum>
      <Increment>0.1</Increment>
      <SortID>700</SortID>
    </Field>
    <Field Def="u8 appearOnAroundSekihi">
      <DisplayName>石碑周辺から出現するか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>石碑周辺から出現するか？</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="u8 disablePCTargetShare">
      <DisplayName>PCとのターゲット共有をスキップするか？</DisplayName>
      <Enum>BOOL_CIRCLECROSS_TYPE</Enum>
      <Description>PCとのターゲット共有をスキップするか？</Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="u8 pcFollowType">
      <DisplayName>PC追従＆ワープタイプ </DisplayName>
      <Enum>BUDDY_PC_FOLLOW_TYPE</Enum>
      <Description>PC追従＆ワープタイプ </Description>
      <EditFlags>None</EditFlags>
      <Maximum>1</Maximum>
      <SortID>1000</SortID>
    </Field>
    <Field Def="dummy8 Reserve[1]">
      <DisplayName>リザーブ</DisplayName>
      <Description>リザーブ</Description>
      <SortID>1113</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv0 = -1">
      <DisplayName>+0時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1100</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv1 = -1">
      <DisplayName>+1時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1101</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv2 = -1">
      <DisplayName>+2時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1102</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv3 = -1">
      <DisplayName>+3時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1103</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv4 = -1">
      <DisplayName>+4時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1104</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv5 = -1">
      <DisplayName>+5時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1105</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv6 = -1">
      <DisplayName>+6時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1106</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv7 = -1">
      <DisplayName>+7時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1107</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv8 = -1">
      <DisplayName>+8時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1108</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv9 = -1">
      <DisplayName>+9時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1109</SortID>
    </Field>
    <Field Def="s32 dopingSpEffect_lv10 = -1">
      <DisplayName>+10時ドーピング特殊効果</DisplayName>
      <Description>+0時ドーピング特殊効果</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>1110</SortID>
    </Field>
    <Field Def="s32 npcPlayerInitParamId = -1">
      <DisplayName>アーキタイプ別初期パラメータID</DisplayName>
      <Description>アーキタイプ別初期パラメータID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>210</SortID>
    </Field>
    <Field Def="s32 generateAnimId = -1">
      <DisplayName>ジェネレートアニメID</DisplayName>
      <Description>ジェネレートアニメID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>750</SortID>
    </Field>
    
    <Field Def="s32 usageShortcutTextId"/>
    <Field Def="s32 unknown_2"/>
    <Field Def="s32 unknown_3"/>
    
    <Field Def="s32 summonBehaviorId"/>
    
    <Field Def="dummy8 padding[4]"  />
    
  </Fields>
</PARAMDEF>