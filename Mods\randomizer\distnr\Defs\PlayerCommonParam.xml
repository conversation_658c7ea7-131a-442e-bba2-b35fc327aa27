<?xml version="1.0" encoding="utf-8"?>
<PARAMDEF XmlVersion="2">
  <ParamType>PLAYER_COMMON_PARAM_ST</ParamType>
  <DataVersion>1</DataVersion>
  <BigEndian>False</BigEndian>
  <Unicode>True</Unicode>
  <FormatVersion>203</FormatVersion>
  <Fields>
    <Field Def="s32 playerFootEffect_bySFX">
      <DisplayName>プレイヤーの自動フットエフェクトのSFX識別子[3桁]</DisplayName>
      <Description>自動フットエフェクトのSFXIDに使われる識別子です。XYYZZZのZZZにあたります。</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999</Maximum>
      <SortID>100</SortID>
    </Field>
    <Field Def="f32 snipeModeDrawAlpha_FadeTime">
      <DisplayName>精密射撃時プレイヤー非表示フェード時間</DisplayName>
      <Description>精密射撃時にプレイヤーを非表示にするときのフェード時間です。単位は秒</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <SortID>200</SortID>
    </Field>
    <Field Def="f32 toughnessRecoverCorrection">
      <DisplayName>プレイヤー強靭度 回復時間補正値</DisplayName>
      <Description>プレイヤーの強靭度回復時間の計算に使われる補正値です。</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <Increment>0.001</Increment>
      <SortID>300</SortID>
    </Field>
    <Field Def="u8 baseMagicSlotSize">
      <DisplayName>魔法記憶スロット初期値</DisplayName>
      <Description>魔法記憶スロット初期値</Description>
      <EditFlags>None</EditFlags>
      <Maximum>99</Maximum>
      <SortID>400</SortID>
    </Field>
    <Field Def="u8 baseAccSlotNum">
      <DisplayName>タリスマン装備スロット初期値</DisplayName>
      <Description>タリスマン装備スロット初期値</Description>
      <EditFlags>None</EditFlags>
      <Maximum>4</Maximum>
      <SortID>500</SortID>
    </Field>
    
    <Field Def="u8 unknownByte_1" />
    <Field Def="u8 unknownByte_2" />
    
    <Field Def="s32 animeID_DropItemPick">
      <DisplayName>ドロップアイテム取得アニメーションID</DisplayName>
      <Description>ドロップアイテムを拾った時のアニメーションID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>700</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Sleep_Player">
      <DisplayName>プレイヤー耐性値回復量_睡眠[point/s]</DisplayName>
      <Description>プレイヤー耐性値回復量_睡眠[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4050</SortID>
    </Field>
    <Field Def="s32 flareOverrideHomingAngle = -1">
      <DisplayName>フレア上書き誘導性能[deg/s]</DisplayName>
      <Description>フレア上書き誘導性能[deg/s](-1:上書きなし)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>99999</Maximum>
      <SortID>6000</SortID>
    </Field>
    <Field Def="f32 flareOverrideHomingStopRange = -1">
      <DisplayName>フレア上書き誘導停止距離[m]</DisplayName>
      <Description>フレア上書き誘導停止距離[m](-1:上書きなし)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <Increment>0.1</Increment>
      <SortID>6100</SortID>
    </Field>
    <Field Def="s32 animeID_SleepCollectorItemPick">
      <DisplayName>ネムリアイテム取得時のアニメーションID</DisplayName>
      <Description>ネムリアイテム取得時のアニメーションID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>900</SortID>
    </Field>
    <Field Def="u32 unlockEventFlagBaseId_forWepAttr">
      <DisplayName>武器属性解禁イベントフラグベースID</DisplayName>
      <Description>武器属性解禁イベントフラグベースID</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>-294967297</Maximum>
      <SortID>7000</SortID>
    </Field>
    <Field Def="s32 systemEnchant_BigRune">
      <DisplayName>システム経由大ルーン発動用特殊効果ID</DisplayName>
      <Description>システムからの大ルーン発動の際に呼び出す特殊効果Id</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>1300</SortID>
    </Field>
    <Field Def="f32 lowStatus_AtkPowDown">
      <DisplayName>ステータス不足 基本攻撃力低下量</DisplayName>
      <Description>ステータス不足 基本攻撃力低下量</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <SortID>1400</SortID>
    </Field>
    <Field Def="f32 lowStatus_ConsumeStaminaRate">
      <DisplayName>ステータス不足 スタミナ消費倍率</DisplayName>
      <Description>ステータス不足 スタミナ消費倍率</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <SortID>1500</SortID>
    </Field>
    <Field Def="s16 lowStatus_AtkGuardBreak">
      <DisplayName>ステータス不足 弾き攻撃力</DisplayName>
      <Description>ステータス不足 弾き攻撃力</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999</Maximum>
      <SortID>1600</SortID>
    </Field>
    <Field Def="s16 guardStatusCorrect_MaxStatusVal">
      <DisplayName>盾ステータス補正 判定ステータス最大値</DisplayName>
      <Description>盾の性能のステータス補正値を計算するときに使う、性能が上昇する最大ステータス値</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99</Maximum>
      <SortID>1700</SortID>
    </Field>
    <Field Def="u16 unlockEventFlagStepNum_forWepAttr = 1">
      <DisplayName>武器属性解禁イベントフラグステップ数</DisplayName>
      <Description>ベースIDから武器属性IDごとにどのぐらい間隔を空けるか。武器属性解禁イベントフラグID＝《武器属性解禁イベントフラグベースID》＋武器属性ID×《武器属性解禁イベントフラグステップ数》</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Minimum>1</Minimum>
      <SortID>7010</SortID>
    </Field>
    <Field Def="u16 retributionMagic_damageCountNum">
      <DisplayName>因果応報_反撃までの被ダメージ回数</DisplayName>
      <Description>因果応報_反撃までの被ダメージ回数</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>999</Maximum>
      <SortID>8000</SortID>
    </Field>
    <Field Def="u16 retributionMagic_damageCountRemainTime">
      <DisplayName>因果応報_反撃までの被ダメージ回数存続時間[s]</DisplayName>
      <Description>因果応報_反撃までの被ダメージ回数存続時間[s]</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>999</Maximum>
      <SortID>8010</SortID>
    </Field>
    <Field Def="u16 retributionMagic_burstDmypolyId">
      <DisplayName>因果応報_反撃魔法ダミポリID</DisplayName>
      <Description>因果応報_反撃魔法ダミポリID</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>999</Maximum>
      <SortID>8030</SortID>
    </Field>
    <Field Def="s32 retributionMagic_burstMagicParamId = -1">
      <DisplayName>因果応報_反撃魔法パラムID</DisplayName>
      <Description>因果応報_反撃魔法パラムID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-1</Minimum>
      <Maximum>999999</Maximum>
      <SortID>8020</SortID>
    </Field>
    <Field Def="f32 chrAimCam_rideOffsetHeight">
      <DisplayName>騎乗精密射撃カメラオフセット高さ</DisplayName>
      <Description>騎乗精密射撃カメラオフセット高さ</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-10</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.1</Increment>
      <SortID>250</SortID>
    </Field>
    
    <Field Def="s32 unknownInt_2" />
    
    <Field Def="s32 arrowCaseWepBindDmypolyId">
      <DisplayName>矢筒の吸着ダミポリID</DisplayName>
      <Description>矢筒の吸着ダミポリID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <SortID>3900</SortID>
    </Field>
    <Field Def="s32 boltPouchWepBindDmypolyId">
      <DisplayName>ボルト筒の吸着ダミポリID</DisplayName>
      <Description>ボルト筒の吸着ダミポリID（ただしボルト筒単独の場合は矢筒のダミポリIDが使われる）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>99999</Maximum>
      <SortID>3950</SortID>
    </Field>
    <Field Def="f32 estusFlaskAllocateRate">
      <DisplayName>マルチ時クライアント瓶補正倍率</DisplayName>
      <Description>マルチ時クライアント瓶補正倍率(0.5指定で所持数半分に)</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>2500</SortID>
    </Field>
    <Field Def="dummy8 reserved38[2]">
      <DisplayName>予約</DisplayName>
      <Description>(dummy8)</Description>
      <DisplayFormat>%d</DisplayFormat>
      <SortID>8033</SortID>
    </Field>
    <Field Def="u8 kickAcceptanceDeg">
      <DisplayName>プレイヤー正面から見てキックを出せる角度</DisplayName>
      <Description>プレイヤー正面から見てキックを出せる角度</Description>
      <DisplayFormat>%u</DisplayFormat>
      <EditFlags>None</EditFlags>
      <Maximum>180</Maximum>
      <SortID>2800</SortID>
    </Field>
    <Field Def="u8 npcPlayerAnalogWeightRate_Light">
      <DisplayName>NPCプレイヤー用アナログ重量比率_軽量</DisplayName>
      <Description>NPCプレイヤー用アナログ重量比率[%]。軽量。</Description>
      <EditFlags>None</EditFlags>
      <Maximum>200</Maximum>
      <SortID>2900</SortID>
    </Field>
    <Field Def="u8 npcPlayerAnalogWeightRate_Normal">
      <DisplayName>NPCプレイヤー用アナログ重量比率_中量</DisplayName>
      <Description>NPCプレイヤー用アナログ重量比率[%]。中量。</Description>
      <EditFlags>None</EditFlags>
      <Maximum>200</Maximum>
      <SortID>3000</SortID>
    </Field>
    <Field Def="u8 npcPlayerAnalogWeightRate_Heavy">
      <DisplayName>NPCプレイヤー用アナログ重量比率_重量</DisplayName>
      <Description>NPCプレイヤー用アナログ重量比率[%]。重量。</Description>
      <EditFlags>None</EditFlags>
      <Maximum>200</Maximum>
      <SortID>3100</SortID>
    </Field>
    <Field Def="u8 npcPlayerAnalogWeightRate_WeightOver">
      <DisplayName>NPCプレイヤー用アナログ重量比率_重量過多</DisplayName>
      <Description>NPCプレイヤー用アナログ重量比率[%]。重量過多。</Description>
      <EditFlags>None</EditFlags>
      <Maximum>200</Maximum>
      <SortID>3200</SortID>
    </Field>
    <Field Def="u8 npcPlayerAnalogWeightRate_SuperLight">
      <DisplayName>NPCプレイヤー用アナログ重量比率_超軽量</DisplayName>
      <Description>NPCプレイヤー用アナログ重量比率[%]。超軽量。</Description>
      <EditFlags>None</EditFlags>
      <Maximum>200</Maximum>
      <SortID>3300</SortID>
    </Field>
    
    <Field Def="s32 unknownInt_1" />
    
    <Field Def="s32 clearCountCorrectBaseSpEffectId">
      <DisplayName>周回補正特殊効果基準ID</DisplayName>
      <Description>周回補正のためにかける特殊効果の基準ID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>3500</SortID>
    </Field>
    <Field Def="s32 arrowBoltModelIdOffset">
      <DisplayName>矢、ボルトのモデルIdオフセット</DisplayName>
      <Description>矢、ボルトモデルを表示する際に、スロット１に装備された場合のモデルIDに加えるオフセット。（モデルId+オフセット値）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999999</Maximum>
      <SortID>3600</SortID>
    </Field>
    <Field Def="s8 arrowBoltRemainingNumModelMaskThreshold1">
      <DisplayName>矢、ボルトの残量によるモデルマスクの残数閾値_1段階[%]</DisplayName>
      <Description>矢、ボルトモデルを表示する際に、本数による表示マスクをかけるときの閾値の１段階判定値[%]。（この値より多ければ１段階表示）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>3700</SortID>
    </Field>
    <Field Def="s8 arrowBoltRemainingNumModelMaskThreshold2">
      <DisplayName>矢、ボルトの残量によるモデルマスクの残数閾値_2段階[%]</DisplayName>
      <Description>矢、ボルトモデルを表示する際に、本数による表示マスクをかけるときの閾値の２段階判定値[%]。（この値より多ければ２段階表示）</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>3800</SortID>
    </Field>
    
    <Field Def="s8 unknownByte_3" />
    <Field Def="s8 unknownByte_4" />
    
    <Field Def="f32 resistRecoverPoint_Poision_Player">
      <DisplayName>プレイヤー耐性値回復量_毒[point/s]</DisplayName>
      <Description>プレイヤー耐性値回復量_毒[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4000</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Desease_Player">
      <DisplayName>プレイヤー耐性値回復量_疫病[point/s]</DisplayName>
      <Description>プレイヤー耐性値回復量_疫病[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4010</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Blood_Player">
      <DisplayName>プレイヤー耐性値回復量_出血[point/s]</DisplayName>
      <Description>プレイヤー耐性値回復量_出血[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4020</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Curse_Player">
      <DisplayName>プレイヤー耐性値回復量_呪い[point/s]</DisplayName>
      <Description>プレイヤー耐性値回復量_呪い[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4030</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Freeze_Player">
      <DisplayName>プレイヤー耐性値回復量_冷気[point/s]</DisplayName>
      <Description>プレイヤー耐性値回復量_冷気[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4040</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Poision_Enemy">
      <DisplayName>敵耐性値回復量_毒[point/s]</DisplayName>
      <Description>敵耐性値回復量_毒[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4500</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Desease_Enemy">
      <DisplayName>敵耐性値回復量_疫病[point/s]</DisplayName>
      <Description>敵耐性値回復量_疫病[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4510</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Blood_Enemy">
      <DisplayName>敵耐性値回復量_出血[point/s]</DisplayName>
      <Description>敵耐性値回復量_出血[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4520</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Curse_Enemy">
      <DisplayName>敵耐性値回復量_呪い[point/s]</DisplayName>
      <Description>敵耐性値回復量_呪い[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4530</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Freeze_Enemy">
      <DisplayName>敵耐性値回復量_冷気[point/s]</DisplayName>
      <Description>敵耐性値回復量_冷気[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4540</SortID>
    </Field>
    <Field Def="f32 requestTimeLeftBothHand">
      <DisplayName>左手両手持ちリクエストのボタン長押し時間[s]</DisplayName>
      <Description>左手両手持ちするときのボタン入力時間</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>10</Maximum>
      <Increment>0.001</Increment>
      <SortID>5000</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Madness_Player">
      <DisplayName>プレイヤー耐性値回復量_発狂[point/s]</DisplayName>
      <Description>プレイヤー耐性値回復量_発狂[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4050</SortID>
    </Field>
    <Field Def="s32 animeID_MaterialItemPick">
      <DisplayName>素材アイテム取得アニメーションID</DisplayName>
      <Description>素材アイテムを拾った時のアニメーションID</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999999</Maximum>
      <SortID>800</SortID>
    </Field>
    <Field Def="f32 hpEstusFlaskAllocateRateForYellowMonk">
      <DisplayName>黄衣HPエスト瓶補正倍率 </DisplayName>
      <Description>黄衣HPエスト瓶補正倍率 </Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5300</SortID>
    </Field>
    <Field Def="s32 hpEstusFlaskAllocateOffsetForYellowMonk">
      <DisplayName>黄衣HPエスト瓶オフセット</DisplayName>
      <Description>黄衣HPエスト瓶オフセット</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-100000</Minimum>
      <Maximum>100000</Maximum>
      <SortID>5400</SortID>
    </Field>
    <Field Def="f32 mpEstusFlaskAllocateRateForYellowMonk">
      <DisplayName>黄衣MPエスト瓶補正倍率</DisplayName>
      <Description>黄衣MPエスト瓶補正倍率</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>100</Maximum>
      <SortID>5500</SortID>
    </Field>
    <Field Def="s32 mpEstusFlaskAllocateOffsetForYellowMonk">
      <DisplayName>黄衣MPエスト瓶オフセット</DisplayName>
      <Description>黄衣MPエスト瓶オフセット</Description>
      <EditFlags>None</EditFlags>
      <Minimum>-100000</Minimum>
      <Maximum>100000</Maximum>
      <SortID>5600</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Sleep_Enemy">
      <DisplayName>敵耐性値回復量_睡眠[point/s]</DisplayName>
      <Description>敵耐性値回復量_睡眠[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4550</SortID>
    </Field>
    <Field Def="f32 resistRecoverPoint_Madness_Enemy">
      <DisplayName>敵耐性値回復量_発狂[point/s]</DisplayName>
      <Description>敵耐性値回復量_発狂[point/s]</Description>
      <EditFlags>None</EditFlags>
      <Minimum>0</Minimum>
      <Maximum>999.99</Maximum>
      <SortID>4560</SortID>
    </Field>
    
    <Field Def="s32 unknown_1" />
    <Field Def="s32 unknown_2" />
    <Field Def="s32 unknown_3" />
    <Field Def="s32 unknown_4" />
    <Field Def="f32 unknown_5" />
    <Field Def="f32 unknown_6" />
    <Field Def="f32 unknown_7" />
    <Field Def="f32 unknown_8" />
    <Field Def="f32 unknown_9" />
    <Field Def="s32 unknown_10" />
    <Field Def="f32 unknown_11" />
    <Field Def="f32 unknown_12" />
    <Field Def="s32 unknown_13" />
    
    <Field Def="s32 unknown_14" />
    <Field Def="s32 revenantSummon1_BuddyParamId" />
    <Field Def="s32 revenantSummon2_BuddyParamId" />
    <Field Def="s32 revenantSummon3_BuddyParamId" />
    <Field Def="f32 unknown_18" />
    <Field Def="s32 unknown_19" />
    <Field Def="f32 unknown_20" />
    <Field Def="f32 unknown_21" />
    <Field Def="s32 unknown_22" />
    <Field Def="s32 unknown_23" />
    
    <Field Def="f32 unknown_24" />
    <Field Def="f32 unknown_25" />
    <Field Def="s32 unknown_26" />
    <Field Def="s32 unknown_27" />
    <Field Def="s32 unknown_28" />
    <Field Def="f32 unknown_29" />
    <Field Def="f32 unknown_30" />
    <Field Def="s32 unknown_31" />
    <Field Def="f32 unknown_32" />
    <Field Def="f32 unknown_33" />
    
    <Field Def="f32 unknown_34" />
    <Field Def="f32 unknown_35" />
    <Field Def="f32 unknown_36" />
    <Field Def="f32 unknown_37" />
    <Field Def="f32 unknown_38" />
    <Field Def="f32 unknown_39" />
    <Field Def="f32 unknown_40" />
    <Field Def="f32 unknown_41" />
    <Field Def="s32 unknown_42" />
    <Field Def="s32 unknown_43" />
    
    <Field Def="s32 unknown_44" />
    <Field Def="f32 unknown_45" />
    <Field Def="s32 unknown_46" />
    <Field Def="f32 unknown_47" />
    
    <Field Def="s32 unknown_48" />
    <Field Def="s32 unknown_49" />
    <Field Def="s32 unknown_50" />
    <Field Def="f32 unknown_51" />
    
  </Fields>
</PARAMDEF>